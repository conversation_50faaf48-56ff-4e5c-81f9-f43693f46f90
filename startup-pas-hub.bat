@echo off
echo ========================================
echo PAS Auto Control Hub Launcher
echo ========================================

:: Set working directory to script location
cd /d "%~dp0"

:: Set environment for production
set NODE_ENV=production

:: Check if dist folder exists (production build)
if exist "dist\index.html" (
  :: Production mode - run electron with built files
  echo [INFO] Running in production mode...
  echo [INFO] Loading from built files...

  :: Start Electron in production mode (show window, not minimized)
  start "" npx electron .

  echo [SUCCESS] PAS Auto Control Hub started successfully!
  echo [INFO] Application is running in system tray.
  echo [INFO] Double-click tray icon to show window.

) else (
  :: Build first if dist doesn't exist
  echo [INFO] Building application for first-time startup...
  call npm run build

  if exist "dist\index.html" (
    echo [SUCCESS] Build completed successfully!
    echo [INFO] Starting PAS Auto Control Hub...
    start "" npx electron .
  ) else (
    echo [ERROR] Build failed! Please run 'npm run build' manually.
    pause
    exit /b 1
  )
)

:: Show completion message
echo.
echo ========================================
echo PAS Auto Control Hub is now running!
echo ========================================
echo.
echo Tips:
echo - Look for the PAS icon in system tray
echo - Double-click tray icon to show window
echo - Right-click tray icon for options
echo - Use tray menu to enable auto-startup
echo.

:: Auto-close after 5 seconds
timeout /t 5 /nobreak > nul
exit
