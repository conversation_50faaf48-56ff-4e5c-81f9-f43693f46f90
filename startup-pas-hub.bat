@echo off
echo Starting PAS Auto Control Hub...

:: Set working directory to script location
cd /d "%~dp0"

:: Check if dist folder exists (production build)
if exist "dist\index.html" (
  :: Production mode - run electron with built files
  echo Running in production mode...
  start "" npx electron .
) else (
  :: Development mode - start dev server and electron
  echo Running in development mode...
  
  :: Start Vite dev server in background
  start "" cmd /c "npm run dev"
  
  :: Wait for dev server to start (10 seconds)
  echo Waiting for dev server to start...
  timeout /t 10 /nobreak > nul
  
  :: Start Electron
  start "" npx electron .
)

:: Exit this script
exit
