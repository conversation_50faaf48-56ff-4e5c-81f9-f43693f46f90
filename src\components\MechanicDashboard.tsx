import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Car, 
  Settings, 
  Zap, 
  Gauge, 
  FileText, 
  Monitor,
  Wrench,
  BarChart3,
  Clock,
  LogOut
} from "lucide-react";
import pasLogo from "@/assets/pas-logo.png";
import { useNavigate } from "react-router-dom";
import { useActivityLogger } from "@/hooks/useActivityLogger";

interface MechanicDashboardProps {
  username: string;
  onLogout: () => void;
}

export default function MechanicDashboard({ username, onLogout }: MechanicDashboardProps) {
  const navigate = useNavigate();
  const { logActivity, endSession } = useActivityLogger();
  const remapApps = [
    {
      id: 1,
      name: "Alat Remap ECU",
      description: "Tuning dan remapping ECU tingkat lanjut",
      icon: Zap,
      status: "available",
      lastUsed: "2 jam lalu",
      category: "Utama"
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON>",
      description: "Monitoring performa secara real-time",
      icon: BarChart3,
      status: "available",
      lastUsed: "1 hari lalu",
      category: "Analisis"
    },
    {
      id: 3,
      name: "Scanner Diagnostik",
      description: "Diagnostik OBD-II dan pemecahan masalah",
      icon: Monitor,
      status: "available",
      lastUsed: "3 jam lalu",
      category: "Diagnostik"
    },
    {
      id: 4,
      name: "Editor Peta Bahan Bakar",
      description: "Optimasi pemetaan injeksi bahan bakar",
      icon: Gauge,
      status: "maintenance",
      lastUsed: "5 hari lalu",
      category: "Tuning"
    },
    {
      id: 5,
      name: "Simulator Dyno",
      description: "Pengujian dynamometer virtual",
      icon: Settings,
      status: "available",
      lastUsed: "1 jam lalu",
      category: "Pengujian"
    },
    {
      id: 6,
      name: "Generator Laporan",
      description: "Membuat laporan tuning detail",
      icon: FileText,
      status: "available",
      lastUsed: "4 jam lalu",
      category: "Dokumentasi"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'maintenance': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'offline': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'available': return 'Tersedia';
      case 'maintenance': return 'Pemeliharaan';
      case 'offline': return 'Offline';
      default: return status;
    }
  };

  const handleAppLaunch = async (appName: string) => {
    await logActivity('app_launch', `Launched ${appName}`, appName);
    navigate(`/remap/${encodeURIComponent(appName)}`);
  };

  const handleLogout = async () => {
    await endSession();
    onLogout();
  };

  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 rounded-lg bg-gradient-primary p-2">
                <img src={pasLogo} alt="PAS Logo" className="w-full h-full object-contain" />
              </div>
              <div>
                <h1 className="text-xl font-bold">PAS Management System</h1>
                <p className="text-sm text-muted-foreground">Stasiun Kerja Mekanik</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="font-medium">Selamat datang, {username}</p>
                <p className="text-sm text-muted-foreground flex items-center">
                  <Wrench className="w-3 h-3 mr-1" />
                  Akses Mekanik
                </p>
              </div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleLogout}
                className="border-destructive/50 text-destructive hover:bg-destructive hover:text-destructive-foreground"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Keluar
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold mb-2">Peluncur Aplikasi Remap</h2>
          <p className="text-muted-foreground">Akses alat profesional untuk tuning dan diagnostik kendaraan</p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-green-500/20 flex items-center justify-center">
                  <Zap className="w-5 h-5 text-green-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">5</p>
                  <p className="text-sm text-muted-foreground">Aplikasi Tersedia</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-yellow-500/20 flex items-center justify-center">
                  <Settings className="w-5 h-5 text-yellow-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">1</p>
                  <p className="text-sm text-muted-foreground">Pemeliharaan</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-blue-500/20 flex items-center justify-center">
                  <Clock className="w-5 h-5 text-blue-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">12</p>
                  <p className="text-sm text-muted-foreground">Jam Hari Ini</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-primary/20 flex items-center justify-center">
                  <Car className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <p className="text-2xl font-bold">8</p>
                  <p className="text-sm text-muted-foreground">Mobil Dituning</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Application Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {remapApps.map((app) => (
            <Card key={app.id} className="bg-gradient-card border-border hover:shadow-primary/20 transition-all duration-300 group cursor-pointer">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className={`w-12 h-12 rounded-xl bg-gradient-primary p-3 group-hover:shadow-glow transition-all duration-300`}>
                    <app.icon className="w-full h-full text-primary-foreground" />
                  </div>
                  <Badge className={`${getStatusColor(app.status)} border`}>
                    {getStatusText(app.status)}
                  </Badge>
                </div>
                <div>
                  <CardTitle className="text-lg mb-1">{app.name}</CardTitle>
                  <CardDescription>{app.description}</CardDescription>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Kategori:</span>
                    <Badge variant="secondary">{app.category}</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Terakhir digunakan:</span>
                    <span>{app.lastUsed}</span>
                  </div>
                  <Button 
                    className="w-full bg-gradient-primary shadow-primary hover:shadow-glow transition-all duration-300"
                    disabled={app.status !== 'available'}
                    onClick={() => handleAppLaunch(app.name)}
                  >
                    {app.status === 'available' ? 'Jalankan Aplikasi' : 'Dalam Pemeliharaan'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </main>
    </div>
  );
}