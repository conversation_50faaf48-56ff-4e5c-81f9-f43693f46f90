import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Settings, Power, PowerOff } from "lucide-react";

declare global {
  interface Window {
    electronAPI?: {
      getAutoLaunchStatus: () => Promise<{
        enabled: boolean;
        error?: string;
      }>;
      enableAutoLaunch: () => Promise<{
        success: boolean;
        message?: string;
        error?: string;
      }>;
      disableAutoLaunch: () => Promise<{
        success: boolean;
        message?: string;
        error?: string;
      }>;
      isElectron: boolean;
    };
  }
}

export default function AutoStartSettings() {
  const [isEnabled, setIsEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const isElectron = window.electronAPI?.isElectron || false;

  // Check current auto-launch status
  const checkAutoLaunchStatus = async () => {
    if (!isElectron || !window.electronAPI) return;

    try {
      setIsLoading(true);
      const result = await window.electronAPI.getAutoLaunchStatus();
      setIsEnabled(result.enabled);
      if (result.error) {
        setError(result.error);
      }
    } catch (err) {
      setError('Failed to check auto-launch status');
      console.error('Error checking auto-launch status:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle auto-launch
  const toggleAutoLaunch = async () => {
    if (!isElectron || !window.electronAPI) return;

    try {
      setIsLoading(true);
      setError(null);

      const result = isEnabled 
        ? await window.electronAPI.disableAutoLaunch()
        : await window.electronAPI.enableAutoLaunch();

      if (result.success) {
        setIsEnabled(!isEnabled);
        alert(result.message || `Auto-launch ${isEnabled ? 'disabled' : 'enabled'} successfully!`);
      } else {
        setError(result.error || 'Failed to toggle auto-launch');
      }
    } catch (err) {
      setError('Failed to toggle auto-launch');
      console.error('Error toggling auto-launch:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkAutoLaunchStatus();
  }, [isElectron]);

  if (!isElectron) {
    return (
      <Card className="bg-gradient-card border-border border-yellow-500/30">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-lg bg-yellow-500/20 flex items-center justify-center">
              <Settings className="w-4 h-4 text-yellow-400" />
            </div>
            <div>
              <p className="font-semibold text-yellow-400">Mode Browser</p>
              <p className="text-sm text-muted-foreground">
                Auto-startup hanya tersedia di mode desktop (Electron)
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gradient-card border-border">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <Settings className="w-5 h-5" />
          <CardTitle>Auto-Start Settings</CardTitle>
        </div>
        <CardDescription>
          Konfigurasi aplikasi untuk berjalan otomatis saat Windows startup
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor="auto-start">Jalankan saat Windows startup</Label>
            <p className="text-sm text-muted-foreground">
              Aplikasi akan terbuka otomatis ketika Windows dinyalakan
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={isEnabled ? "default" : "secondary"}>
              {isEnabled ? "Aktif" : "Nonaktif"}
            </Badge>
            <Switch
              id="auto-start"
              checked={isEnabled}
              onCheckedChange={toggleAutoLaunch}
              disabled={isLoading}
            />
          </div>
        </div>

        {error && (
          <div className="p-3 rounded-lg bg-red-500/10 border border-red-500/20">
            <p className="text-sm text-red-400">{error}</p>
          </div>
        )}

        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={checkAutoLaunchStatus}
            disabled={isLoading}
          >
            <Power className="w-4 h-4 mr-2" />
            Refresh Status
          </Button>
          
          <Button
            variant={isEnabled ? "destructive" : "default"}
            size="sm"
            onClick={toggleAutoLaunch}
            disabled={isLoading}
          >
            {isEnabled ? (
              <>
                <PowerOff className="w-4 h-4 mr-2" />
                Nonaktifkan
              </>
            ) : (
              <>
                <Power className="w-4 h-4 mr-2" />
                Aktifkan
              </>
            )}
          </Button>
        </div>

        <div className="text-xs text-muted-foreground space-y-1">
          <p>• Auto-start menggunakan Windows Registry</p>
          <p>• Aplikasi akan terbuka di halaman login</p>
          <p>• Dapat dinonaktifkan kapan saja</p>
        </div>
      </CardContent>
    </Card>
  );
}
