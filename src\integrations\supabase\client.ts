// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://oyfxcmblwqkvszwcqrtj.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im95ZnhjbWJsd3FrdnN6d2NxcnRqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2NTMxNjUsImV4cCI6MjA2ODIyOTE2NX0.4XkK57z8_9F-Rbfhg96-lOxfFBH5JpkKBhM9-oykZ10";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});