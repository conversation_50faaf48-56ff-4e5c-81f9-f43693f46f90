import { useState } from "react";
import { useActivityLogger } from "@/hooks/useActivityLogger";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Car, Settings, Users, Shield } from "lucide-react";
import pasLogo from "@/assets/pas-logo.png";

interface LoginScreenProps {
  onLogin: (username: string, role: 'mechanic' | 'owner') => void;
}

export default function LoginScreen({ onLogin }: LoginScreenProps) {
  const { startSession } = useActivityLogger();
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [selectedRole, setSelectedRole] = useState<'mechanic' | 'owner'>('mechanic');

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Simulate authentication
    if (username && password) {
      await startSession();
      onLogin(username, selectedRole);
    } else {
      alert("Silakan masukkan username dan password");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-dark flex items-center justify-center p-6">
      <div className="w-full max-w-md space-y-8">
        {/* Logo and Header */}
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="w-20 h-20 rounded-2xl bg-gradient-primary p-4 shadow-glow">
              <img src={pasLogo} alt="PAS Logo" className="w-full h-full object-contain" />
            </div>
          </div>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              PAS MANAGEMENT SYSTEM
            </h1>
            <p className="text-muted-foreground mt-2">Solusi Otomotif Profesional</p>
          </div>
        </div>

        {/* Login Form */}
        <Card className="bg-gradient-card border-border shadow-card">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">Masuk</CardTitle>
            <CardDescription className="text-center">
              Pilih role dan masukkan kredensial Anda
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Role Selection */}
            <div className="space-y-3">
              <Label>Role</Label>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  type="button"
                  variant={selectedRole === 'mechanic' ? 'default' : 'secondary'}
                  className={`h-12 ${selectedRole === 'mechanic' ? 'bg-gradient-primary shadow-primary' : ''}`}
                  onClick={() => setSelectedRole('mechanic')}
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Mekanik
                </Button>
                <Button
                  type="button"
                  variant={selectedRole === 'owner' ? 'default' : 'secondary'}
                  className={`h-12 ${selectedRole === 'owner' ? 'bg-gradient-primary shadow-primary' : ''}`}
                  onClick={() => setSelectedRole('owner')}
                >
                  <Shield className="w-4 h-4 mr-2" />
                  Pemilik
                </Button>
              </div>
            </div>

            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Nama Pengguna</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="Masukkan username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="h-11"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Kata Sandi</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Masukkan password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="h-11"
                  required
                />
              </div>
              <Button 
                type="submit" 
                className="w-full h-12 bg-gradient-primary shadow-primary hover:shadow-glow transition-all duration-300"
              >
                <Car className="w-4 h-4 mr-2" />
                Masuk ke Sistem
              </Button>
            </form>

            {/* Role Info */}
            <div className="pt-4 border-t border-border">
              <div className="flex items-center justify-center space-x-2">
                {selectedRole === 'mechanic' ? (
                  <Badge variant="secondary" className="bg-secondary/50">
                    <Settings className="w-3 h-3 mr-1" />
                    Akses Aplikasi Remap
                  </Badge>
                ) : (
                  <Badge variant="secondary" className="bg-secondary/50">
                    <Users className="w-3 h-3 mr-1" />
                    Dashboard & Kontrol Pengguna
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-sm text-muted-foreground">
          <p>© 2024 PAS Management System</p>
          <p>Solusi Bengkel Otomotif Profesional</p>
        </div>
      </div>
    </div>
  );
}