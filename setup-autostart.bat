@echo off
echo ========================================
echo PAS Auto Control Hub - Auto-Start Setup
echo ========================================
echo.

set APP_NAME=PAS Auto Control Hub
set APP_PATH=%~dp0run-electron.bat
set STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup

echo Current directory: %~dp0
echo App path: %APP_PATH%
echo Startup folder: %STARTUP_FOLDER%
echo.

:menu
echo Pilih opsi:
echo 1. Aktifkan auto-start (tambah ke Windows Startup)
echo 2. Nonaktifkan auto-start (hapus dari Windows Startup)
echo 3. Cek status auto-start
echo 4. Keluar
echo.
set /p choice="Masukkan pilihan (1-4): "

if "%choice%"=="1" goto enable
if "%choice%"=="2" goto disable
if "%choice%"=="3" goto status
if "%choice%"=="4" goto exit
echo Pilihan tidak valid!
goto menu

:enable
echo.
echo Mengaktifkan auto-start...
copy "%APP_PATH%" "%STARTUP_FOLDER%\%APP_NAME%.bat" >nul
if %errorlevel%==0 (
    echo ✅ Auto-start berhasil diaktifkan!
    echo Aplikasi akan berjalan otomatis saat Windows startup.
) else (
    echo ❌ Gagal mengaktifkan auto-start!
)
echo.
pause
goto menu

:disable
echo.
echo Menonaktifkan auto-start...
del "%STARTUP_FOLDER%\%APP_NAME%.bat" >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Auto-start berhasil dinonaktifkan!
) else (
    echo ⚠️  Auto-start mungkin sudah tidak aktif.
)
echo.
pause
goto menu

:status
echo.
echo Mengecek status auto-start...
if exist "%STARTUP_FOLDER%\%APP_NAME%.bat" (
    echo ✅ Auto-start: AKTIF
    echo File: %STARTUP_FOLDER%\%APP_NAME%.bat
) else (
    echo ❌ Auto-start: TIDAK AKTIF
)
echo.
pause
goto menu

:exit
echo.
echo Terima kasih telah menggunakan PAS Auto Control Hub!
pause
exit
