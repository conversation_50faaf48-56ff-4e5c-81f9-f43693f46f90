import { useState } from "react";
import LoginScreen from "@/components/LoginScreen";
import MechanicDashboard from "@/components/MechanicDashboard";
import OwnerDashboard from "@/components/OwnerDashboard";

const Index = () => {
  const [user, setUser] = useState<{ username: string; role: 'mechanic' | 'owner' } | null>(null);

  const handleLogin = (username: string, role: 'mechanic' | 'owner') => {
    setUser({ username, role });
  };

  const handleLogout = () => {
    setUser(null);
  };

  if (!user) {
    return <LoginScreen onLogin={handleLogin} />;
  }

  if (user.role === 'mechanic') {
    return <MechanicDashboard username={user.username} onLogout={handleLogout} />;
  }

  return <OwnerDashboard username={user.username} onLogout={handleLogout} />;
};

export default Index;
