import { <PERSON>, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Users, 
  Activity, 
  Shield, 
  Settings, 
  LogOut,
  Eye,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  User,
  Monitor,
  BarChart3,
  Calendar
} from "lucide-react";
import pasLogo from "@/assets/pas-logo.png";
import { useActivityLogger } from "@/hooks/useActivityLogger";

interface OwnerDashboardProps {
  username: string;
  onLogout: () => void;
}

export default function OwnerDashboard({ username, onLogout }: OwnerDashboardProps) {
  const { endSession } = useActivityLogger();
  const users = [
    {
      id: 1,
      username: "mekanik001",
      name: "<PERSON>",
      role: "Senior Mechanic",
      status: "online",
      lastActivity: "2 menit lalu",
      appsUsed: 5,
      hoursToday: 7.5
    },
    {
      id: 2,
      username: "mekanik002", 
      name: "<PERSON><PERSON>",
      role: "Junior Mechanic",
      status: "offline",
      lastActivity: "1 jam lalu",
      appsUsed: 3,
      hoursToday: 6.0
    },
    {
      id: 3,
      username: "mekanik003",
      name: "Candra Wijaya",
      role: "Specialist",
      status: "online",
      lastActivity: "15 menit lalu",
      appsUsed: 8,
      hoursToday: 8.2
    }
  ];

  const recentLogs = [
    {
      id: 1,
      user: "Ahmad Rizki",
      action: "Launched ECU Remapping Tool",
      timestamp: "2024-07-16 14:30:15",
      status: "success",
      details: "Vehicle: Honda Civic 2019"
    },
    {
      id: 2,
      user: "Candra Wijaya",
      action: "Completed Performance Analysis",
      timestamp: "2024-07-16 14:15:42",
      status: "success",
      details: "Vehicle: Toyota Camry 2021"
    },
    {
      id: 3,
      user: "Budi Santoso",
      action: "Failed Login Attempt",
      timestamp: "2024-07-16 13:45:18",
      status: "warning",
      details: "Multiple failed attempts detected"
    },
    {
      id: 4,
      user: "Ahmad Rizki",
      action: "Generated Tuning Report",
      timestamp: "2024-07-16 13:20:05",
      status: "success",
      details: "Report ID: RPT-2024-001"
    },
    {
      id: 5,
      user: "System",
      action: "Backup Completed",
      timestamp: "2024-07-16 12:00:00",
      status: "info",
      details: "Daily system backup successful"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'offline': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      case 'away': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getLogStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-400" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-400" />;
      case 'info': return <Activity className="w-4 h-4 text-blue-400" />;
      default: return <Activity className="w-4 h-4 text-gray-400" />;
    }
  };

  const handleLogout = async () => {
    await endSession();
    onLogout();
  };

  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 rounded-lg bg-gradient-primary p-2">
                <img src={pasLogo} alt="PAS Logo" className="w-full h-full object-contain" />
              </div>
              <div>
                <h1 className="text-xl font-bold">PAS Management System</h1>
                <p className="text-sm text-muted-foreground">Owner Control Panel</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="font-medium">Welcome, {username}</p>
                <p className="text-sm text-muted-foreground flex items-center">
                  <Shield className="w-3 h-3 mr-1" />
                  Administrator Access
                </p>
              </div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleLogout}
                className="border-destructive/50 text-destructive hover:bg-destructive hover:text-destructive-foreground"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold mb-2">Control Panel Dashboard</h2>
          <p className="text-muted-foreground">Monitor user activity, manage access, and view system logs</p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-green-500/20 flex items-center justify-center">
                  <Users className="w-5 h-5 text-green-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">2</p>
                  <p className="text-sm text-muted-foreground">Online Users</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-blue-500/20 flex items-center justify-center">
                  <Activity className="w-5 h-5 text-blue-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">47</p>
                  <p className="text-sm text-muted-foreground">Actions Today</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-primary/20 flex items-center justify-center">
                  <Monitor className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <p className="text-2xl font-bold">6</p>
                  <p className="text-sm text-muted-foreground">Apps Active</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-yellow-500/20 flex items-center justify-center">
                  <AlertTriangle className="w-5 h-5 text-yellow-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">1</p>
                  <p className="text-sm text-muted-foreground">Warnings</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs Content */}
        <Tabs defaultValue="users" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-card">
            <TabsTrigger value="users" className="flex items-center space-x-2">
              <Users className="w-4 h-4" />
              <span>User Management</span>
            </TabsTrigger>
            <TabsTrigger value="logs" className="flex items-center space-x-2">
              <Eye className="w-4 h-4" />
              <span>Activity Logs</span>
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center space-x-2">
              <BarChart3 className="w-4 h-4" />
              <span>Analytics</span>
            </TabsTrigger>
          </TabsList>

          {/* User Management Tab */}
          <TabsContent value="users" className="space-y-6">
            <Card className="bg-gradient-card border-border">
              <CardHeader>
                <CardTitle>Active Users</CardTitle>
                <CardDescription>Manage mechanic accounts and monitor their activity</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {users.map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-4 rounded-lg bg-secondary/20 border border-border">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 rounded-full bg-gradient-primary flex items-center justify-center">
                          <User className="w-5 h-5 text-primary-foreground" />
                        </div>
                        <div>
                          <h4 className="font-medium">{user.name}</h4>
                          <p className="text-sm text-muted-foreground">@{user.username} • {user.role}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right text-sm">
                          <p>Apps: {user.appsUsed} | Hours: {user.hoursToday}h</p>
                          <p className="text-muted-foreground">{user.lastActivity}</p>
                        </div>
                        <Badge className={getStatusColor(user.status)}>
                          {user.status}
                        </Badge>
                        <Button size="sm" variant="outline">
                          <Settings className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Activity Logs Tab */}
          <TabsContent value="logs" className="space-y-6">
            <Card className="bg-gradient-card border-border">
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Real-time system and user activity monitoring</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentLogs.map((log) => (
                    <div key={log.id} className="flex items-center space-x-4 p-3 rounded-lg bg-secondary/10 border border-border/50">
                      <div className="flex-shrink-0">
                        {getLogStatusIcon(log.status)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">{log.action}</p>
                        <p className="text-sm text-muted-foreground">{log.details}</p>
                      </div>
                      <div className="text-right text-sm text-muted-foreground">
                        <p>{log.user}</p>
                        <p className="flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {log.timestamp.split(' ')[1]}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="bg-gradient-card border-border">
                <CardHeader>
                  <CardTitle>Daily Usage</CardTitle>
                  <CardDescription>Application usage patterns</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>ECU Remapping Tool</span>
                      <span className="font-medium">85%</span>
                    </div>
                    <div className="w-full bg-secondary/30 rounded-full h-2">
                      <div className="bg-gradient-primary h-2 rounded-full" style={{ width: '85%' }}></div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Performance Analyzer</span>
                      <span className="font-medium">67%</span>
                    </div>
                    <div className="w-full bg-secondary/30 rounded-full h-2">
                      <div className="bg-gradient-secondary h-2 rounded-full" style={{ width: '67%' }}></div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Diagnostic Scanner</span>
                      <span className="font-medium">45%</span>
                    </div>
                    <div className="w-full bg-secondary/30 rounded-full h-2">
                      <div className="bg-gradient-primary h-2 rounded-full" style={{ width: '45%' }}></div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-card border-border">
                <CardHeader>
                  <CardTitle>Weekly Summary</CardTitle>
                  <CardDescription>System performance metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 rounded-lg bg-secondary/10">
                      <p className="text-2xl font-bold text-green-400">99.8%</p>
                      <p className="text-sm text-muted-foreground">Uptime</p>
                    </div>
                    <div className="text-center p-4 rounded-lg bg-secondary/10">
                      <p className="text-2xl font-bold text-blue-400">156</p>
                      <p className="text-sm text-muted-foreground">Sessions</p>
                    </div>
                    <div className="text-center p-4 rounded-lg bg-secondary/10">
                      <p className="text-2xl font-bold text-primary">42h</p>
                      <p className="text-sm text-muted-foreground">Total Usage</p>
                    </div>
                    <div className="text-center p-4 rounded-lg bg-secondary/10">
                      <p className="text-2xl font-bold text-accent">23</p>
                      <p className="text-sm text-muted-foreground">Cars Tuned</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}