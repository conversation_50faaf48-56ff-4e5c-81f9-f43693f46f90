"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
electron_1.contextBridge.exposeInMainWorld('electronAPI', {
    launchApp: (executable, name, paths) => electron_1.ipcRenderer.invoke('launch-app', { executable, name, paths }),
    startApp: (executable, name) => electron_1.ipcRenderer.invoke('start-app', { executable, name }),
    openExternal: (url) => electron_1.ipcRenderer.invoke('open-external', url),
    checkAppInstalled: (executable, paths) => electron_1.ipcRenderer.invoke('check-app-installed', { executable, paths }),
    // Platform info
    platform: process.platform,
    isElectron: true
});
//# sourceMappingURL=preload.js.map