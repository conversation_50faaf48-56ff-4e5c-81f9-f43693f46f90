const express = require('express');
const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const cors = require('cors');

const app = express();
const PORT = 3003;

// Middleware
app.use(cors());
app.use(express.json());

// Common installation paths for remap applications
const commonPaths = {
  'winols.exe': [
    'C:\\Program Files\\EVC\\WinOLS\\winols.exe',
    'C:\\Program Files (x86)\\EVC\\WinOLS\\winols.exe',
    'C:\\WinOLS\\winols.exe',
    'D:\\WinOLS\\winols.exe'
  ],
  'kessv2.exe': [
    'C:\\Program Files\\Alientech\\KESS V2\\kessv2.exe',
    'C:\\Program Files (x86)\\Alientech\\KESS V2\\kessv2.exe',
    'C:\\KESS V2\\kessv2.exe',
    'D:\\KESS V2\\kessv2.exe'
  ],
  'ktag.exe': [
    'C:\\Program Files\\Alientech\\KTAG\\ktag.exe',
    'C:\\Program Files (x86)\\Alientech\\KTAG\\ktag.exe',
    'C:\\KTAG\\ktag.exe',
    'D:\\KTAG\\ktag.exe'
  ],
  'autotuner.exe': [
    'C:\\Program Files\\AutoTuner\\autotuner.exe',
    'C:\\Program Files (x86)\\AutoTuner\\autotuner.exe',
    'C:\\AutoTuner\\autotuner.exe',
    'D:\\AutoTuner\\autotuner.exe'
  ],
  'pcmtuner.exe': [
    'C:\\Program Files\\PCMTuner\\pcmtuner.exe',
    'C:\\Program Files (x86)\\PCMTuner\\pcmtuner.exe',
    'C:\\PCMTuner\\pcmtuner.exe',
    'D:\\PCMTuner\\pcmtuner.exe'
  ],
  'diamond.exe': [
    'C:\\Program Files\\DiamonD\\diamond.exe',
    'C:\\Program Files (x86)\\DiamonD\\diamond.exe',
    'C:\\DiamonD\\diamond.exe',
    'D:\\DiamonD\\diamond.exe'
  ],
  'swiftec.exe': [
    'C:\\Program Files\\Swiftec\\swiftec.exe',
    'C:\\Program Files (x86)\\Swiftec\\swiftec.exe',
    'C:\\Swiftec\\swiftec.exe',
    'D:\\Swiftec\\swiftec.exe'
  ]
};

// Function to find executable
function findExecutable(executableName) {
  const paths = commonPaths[executableName] || [];
  
  for (const execPath of paths) {
    if (fs.existsSync(execPath)) {
      return execPath;
    }
  }
  
  return null;
}

// API endpoint to launch application
app.post('/api/launch-app', (req, res) => {
  const { executable, name } = req.body;
  
  console.log(`Attempting to launch: ${name} (${executable})`);
  
  // Find the executable
  const executablePath = findExecutable(executable);
  
  if (!executablePath) {
    return res.json({
      success: false,
      error: `Executable ${executable} not found in common installation paths`
    });
  }
  
  // Launch the application
  try {
    const child = spawn(executablePath, [], {
      detached: true,
      stdio: 'ignore'
    });
    
    child.unref();
    
    console.log(`Successfully launched: ${name} from ${executablePath}`);
    
    res.json({
      success: true,
      message: `Successfully launched ${name}`,
      path: executablePath
    });
  } catch (error) {
    console.error(`Error launching ${name}:`, error);
    res.json({
      success: false,
      error: error.message
    });
  }
});

// Alternative API endpoint using Windows start command
app.post('/api/start-app', (req, res) => {
  const { executable, name } = req.body;
  
  console.log(`Attempting to start: ${name} using Windows start command`);
  
  // Try using Windows start command
  exec(`start "" "${executable}"`, (error, stdout, stderr) => {
    if (error) {
      console.error(`Error starting ${name}:`, error);
      return res.json({
        success: false,
        error: error.message
      });
    }
    
    console.log(`Successfully started: ${name} using start command`);
    
    res.json({
      success: true,
      message: `Successfully started ${name} using Windows start command`
    });
  });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'PAS Remap API Server is running' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 PAS Remap API Server running on http://localhost:${PORT}`);
  console.log(`📁 Monitoring common installation paths for remap applications`);
});

module.exports = app;
