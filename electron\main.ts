import { app, BrowserWindow, ipc<PERSON>ain, shell, Tray, Menu, nativeImage } from 'electron';
import { spawn, exec } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';
import AutoLaunch from 'auto-launch';

const isDev = process.env.NODE_ENV === 'development';

// Auto-launch configuration
const autoLauncher = new AutoLaunch({
  name: 'PAS Auto Control Hub',
  path: app.getPath('exe'),
  isHidden: false, // Set to true if you want it to start minimized
});

// Global variables
let mainWindow: BrowserWindow | null = null;
let tray: Tray | null = null;
let isQuiting = false;

// Common installation paths for remap applications (now empty - will use custom paths)
const commonPaths: { [key: string]: string[] } = {};

function createWindow(): void {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../public/favicon.ico'),
    title: 'PAS Auto Control Hub',
    show: false,
    center: true,
    resizable: true,
    minimizable: true,
    maximizable: true,
    closable: true,
    skipTaskbar: false, // Ensure it appears in taskbar
    alwaysOnTop: false
  });

  // Load the app
  if (isDev) {
    mainWindow.loadURL('http://localhost:8080');
    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show();
      mainWindow.focus();
      mainWindow.center();

      // Ensure window is visible and on top
      mainWindow.setAlwaysOnTop(true);
      setTimeout(() => {
        if (mainWindow) {
          mainWindow.setAlwaysOnTop(false);
        }
      }, 1000);
    }
  });

  // Handle window closed - minimize to tray instead of quit
  mainWindow.on('close', (event) => {
    if (!isQuiting) {
      event.preventDefault();
      if (mainWindow) {
        mainWindow.hide();
      }
    }
    return false;
  });

  // Create system tray
  createTray();
}

function createTray(): void {
  // Create tray icon (you can replace with your own icon)
  const iconPath = path.join(__dirname, '../public/favicon.ico');
  const trayIcon = nativeImage.createFromPath(iconPath);

  tray = new Tray(trayIcon);

  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Show PAS Auto Control Hub',
      click: () => {
        if (mainWindow) {
          mainWindow.show();
          mainWindow.focus();
        }
      }
    },
    {
      label: 'Hide to Tray',
      click: () => {
        if (mainWindow) {
          mainWindow.hide();
        }
      }
    },
    { type: 'separator' },
    {
      label: 'Auto-Start Settings',
      submenu: [
        {
          label: 'Enable Auto-Start',
          click: async () => {
            try {
              await autoLauncher.enable();
              console.log('Auto-start enabled');
            } catch (error) {
              console.error('Error enabling auto-start:', error);
            }
          }
        },
        {
          label: 'Disable Auto-Start',
          click: async () => {
            try {
              await autoLauncher.disable();
              console.log('Auto-start disabled');
            } catch (error) {
              console.error('Error disabling auto-start:', error);
            }
          }
        }
      ]
    },
    { type: 'separator' },
    {
      label: 'Quit',
      click: () => {
        isQuiting = true;
        app.quit();
      }
    }
  ]);

  tray.setToolTip('PAS Auto Control Hub');
  tray.setContextMenu(contextMenu);

  // Double click to show window
  tray.on('double-click', () => {
    if (mainWindow) {
      mainWindow.show();
      mainWindow.focus();
    }
  });
}

// Function to find executable
function findExecutable(executableName: string, customPaths: string[] = []): string | null {
  const paths = customPaths.length > 0 ? customPaths : (commonPaths[executableName] || []);

  for (const execPath of paths) {
    if (fs.existsSync(execPath)) {
      return execPath;
    }
  }

  return null;
}

// IPC handlers
ipcMain.handle('launch-app', async (event, { executable, name, paths }) => {
  try {
    console.log(`Attempting to launch: ${name} (${executable})`);

    // Find the executable
    const executablePath = findExecutable(executable, paths);

    if (!executablePath) {
      return {
        success: false,
        error: `Executable ${executable} not found in provided paths`
      };
    }

    // Launch the application
    const child = spawn(executablePath, [], {
      detached: true,
      stdio: 'ignore'
    });

    child.unref();

    console.log(`Successfully launched: ${name} from ${executablePath}`);

    return {
      success: true,
      message: `Successfully launched ${name}`,
      path: executablePath
    };
  } catch (error: any) {
    console.error(`Error launching ${name}:`, error);
    return {
      success: false,
      error: error.message
    };
  }
});

ipcMain.handle('start-app', async (event, { executable, name }) => {
  return new Promise((resolve) => {
    console.log(`Attempting to start: ${name} using Windows start command`);
    
    // Try using Windows start command
    exec(`start "" "${executable}"`, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error starting ${name}:`, error);
        resolve({
          success: false,
          error: error.message
        });
        return;
      }
      
      console.log(`Successfully started: ${name} using start command`);
      
      resolve({
        success: true,
        message: `Successfully started ${name} using Windows start command`
      });
    });
  });
});

ipcMain.handle('open-external', async (event, url) => {
  shell.openExternal(url);
});

// Auto-launch handlers
ipcMain.handle('get-auto-launch-status', async () => {
  try {
    const isEnabled = await autoLauncher.isEnabled();
    return { enabled: isEnabled };
  } catch (error: any) {
    console.error('Error checking auto-launch status:', error);
    return { enabled: false, error: error.message };
  }
});

ipcMain.handle('enable-auto-launch', async () => {
  try {
    await autoLauncher.enable();
    return { success: true, message: 'Auto-launch enabled successfully' };
  } catch (error: any) {
    console.error('Error enabling auto-launch:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('disable-auto-launch', async () => {
  try {
    await autoLauncher.disable();
    return { success: true, message: 'Auto-launch disabled successfully' };
  } catch (error: any) {
    console.error('Error disabling auto-launch:', error);
    return { success: false, error: error.message };
  }
});

// Window management handlers
ipcMain.handle('show-window', async () => {
  if (mainWindow) {
    mainWindow.show();
    mainWindow.focus();
    mainWindow.center();
    mainWindow.setAlwaysOnTop(true);
    setTimeout(() => {
      if (mainWindow) {
        mainWindow.setAlwaysOnTop(false);
      }
    }, 1000);
    return { success: true };
  }
  return { success: false, error: 'Window not available' };
});

ipcMain.handle('hide-window', async () => {
  if (mainWindow) {
    mainWindow.hide();
    return { success: true };
  }
  return { success: false, error: 'Window not available' };
});

ipcMain.handle('check-app-installed', async (event, { executable, paths }) => {
  try {
    console.log(`Checking installation for: ${executable}`);

    // Check each path
    for (const execPath of paths) {
      if (fs.existsSync(execPath)) {
        console.log(`Found ${executable} at: ${execPath}`);

        // Try to get version info (optional)
        let version = 'Terdeteksi';
        try {
          // You can add version detection logic here if needed
          const stats = fs.statSync(execPath);
          version = `Terdeteksi (${stats.mtime.toLocaleDateString()})`;
        } catch (versionError) {
          // Version detection failed, use default
        }

        return {
          found: true,
          path: execPath,
          version: version
        };
      }
    }

    console.log(`${executable} not found in any provided paths`);
    return {
      found: false,
      path: null,
      version: null
    };
  } catch (error: any) {
    console.error(`Error checking ${executable}:`, error);
    return {
      found: false,
      path: null,
      version: null,
      error: error.message
    };
  }
});

// App event handlers
app.whenReady().then(() => {
  createWindow();

  // Enable auto-launch by default in production
  if (!isDev) {
    autoLauncher.isEnabled().then((isEnabled) => {
      if (!isEnabled) {
        console.log('Enabling auto-launch for production...');
        autoLauncher.enable().catch((error) => {
          console.error('Failed to enable auto-launch:', error);
        });
      }
    });
  }
});

app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  if (process.platform === 'darwin') {
    return;
  }

  // On Windows/Linux, keep running in tray
  if (tray) {
    return;
  }

  app.quit();
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  } else if (mainWindow) {
    mainWindow.show();
    mainWindow.focus();
  }
});

// Prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // Someone tried to run a second instance, focus our window instead
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
      mainWindow.show();
    }
  });
}

// Handle app being launched at startup
app.on('ready', () => {
  // Check if app was launched at startup
  const wasOpenedAtLogin = app.getLoginItemSettings().wasOpenedAtLogin;

  if (wasOpenedAtLogin) {
    console.log('App was launched at startup');
    // Show window even at startup for login
    setTimeout(() => {
      if (mainWindow) {
        mainWindow.show();
        mainWindow.focus();
        mainWindow.center();
      }
    }, 2000); // Delay 2 seconds to ensure everything is loaded
  }
});
