"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const child_process_1 = require("child_process");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const auto_launch_1 = __importDefault(require("auto-launch"));
const isDev = process.env.NODE_ENV === 'development';
// Auto-launch configuration
const autoLauncher = new auto_launch_1.default({
    name: 'PAS Auto Control Hub',
    path: electron_1.app.getPath('exe'),
    isHidden: false, // Set to true if you want it to start minimized
});
// Global variables
let mainWindow = null;
let tray = null;
let isQuiting = false;
// Common installation paths for remap applications (now empty - will use custom paths)
const commonPaths = {};
function createWindow() {
    // Create the browser window
    mainWindow = new electron_1.BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        },
        icon: path.join(__dirname, '../public/favicon.ico'),
        title: 'PAS Auto Control Hub',
        show: false
    });
    // Load the app
    if (isDev) {
        mainWindow.loadURL('http://localhost:8080');
        // Open DevTools in development
        mainWindow.webContents.openDevTools();
    }
    else {
        mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
    }
    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        if (mainWindow) {
            mainWindow.show();
        }
    });
    // Handle window closed - minimize to tray instead of quit
    mainWindow.on('close', (event) => {
        if (!isQuiting) {
            event.preventDefault();
            if (mainWindow) {
                mainWindow.hide();
            }
        }
        return false;
    });
    // Create system tray
    createTray();
}
function createTray() {
    // Create tray icon (you can replace with your own icon)
    const iconPath = path.join(__dirname, '../public/favicon.ico');
    const trayIcon = electron_1.nativeImage.createFromPath(iconPath);
    tray = new electron_1.Tray(trayIcon);
    const contextMenu = electron_1.Menu.buildFromTemplate([
        {
            label: 'Show PAS Auto Control Hub',
            click: () => {
                if (mainWindow) {
                    mainWindow.show();
                    mainWindow.focus();
                }
            }
        },
        {
            label: 'Hide to Tray',
            click: () => {
                if (mainWindow) {
                    mainWindow.hide();
                }
            }
        },
        { type: 'separator' },
        {
            label: 'Auto-Start Settings',
            submenu: [
                {
                    label: 'Enable Auto-Start',
                    click: async () => {
                        try {
                            await autoLauncher.enable();
                            console.log('Auto-start enabled');
                        }
                        catch (error) {
                            console.error('Error enabling auto-start:', error);
                        }
                    }
                },
                {
                    label: 'Disable Auto-Start',
                    click: async () => {
                        try {
                            await autoLauncher.disable();
                            console.log('Auto-start disabled');
                        }
                        catch (error) {
                            console.error('Error disabling auto-start:', error);
                        }
                    }
                }
            ]
        },
        { type: 'separator' },
        {
            label: 'Quit',
            click: () => {
                isQuiting = true;
                electron_1.app.quit();
            }
        }
    ]);
    tray.setToolTip('PAS Auto Control Hub');
    tray.setContextMenu(contextMenu);
    // Double click to show window
    tray.on('double-click', () => {
        if (mainWindow) {
            mainWindow.show();
            mainWindow.focus();
        }
    });
}
// Function to find executable
function findExecutable(executableName, customPaths = []) {
    const paths = customPaths.length > 0 ? customPaths : (commonPaths[executableName] || []);
    for (const execPath of paths) {
        if (fs.existsSync(execPath)) {
            return execPath;
        }
    }
    return null;
}
// IPC handlers
electron_1.ipcMain.handle('launch-app', async (event, { executable, name, paths }) => {
    try {
        console.log(`Attempting to launch: ${name} (${executable})`);
        // Find the executable
        const executablePath = findExecutable(executable, paths);
        if (!executablePath) {
            return {
                success: false,
                error: `Executable ${executable} not found in provided paths`
            };
        }
        // Launch the application
        const child = (0, child_process_1.spawn)(executablePath, [], {
            detached: true,
            stdio: 'ignore'
        });
        child.unref();
        console.log(`Successfully launched: ${name} from ${executablePath}`);
        return {
            success: true,
            message: `Successfully launched ${name}`,
            path: executablePath
        };
    }
    catch (error) {
        console.error(`Error launching ${name}:`, error);
        return {
            success: false,
            error: error.message
        };
    }
});
electron_1.ipcMain.handle('start-app', async (event, { executable, name }) => {
    return new Promise((resolve) => {
        console.log(`Attempting to start: ${name} using Windows start command`);
        // Try using Windows start command
        (0, child_process_1.exec)(`start "" "${executable}"`, (error, stdout, stderr) => {
            if (error) {
                console.error(`Error starting ${name}:`, error);
                resolve({
                    success: false,
                    error: error.message
                });
                return;
            }
            console.log(`Successfully started: ${name} using start command`);
            resolve({
                success: true,
                message: `Successfully started ${name} using Windows start command`
            });
        });
    });
});
electron_1.ipcMain.handle('open-external', async (event, url) => {
    electron_1.shell.openExternal(url);
});
// Auto-launch handlers
electron_1.ipcMain.handle('get-auto-launch-status', async () => {
    try {
        const isEnabled = await autoLauncher.isEnabled();
        return { enabled: isEnabled };
    }
    catch (error) {
        console.error('Error checking auto-launch status:', error);
        return { enabled: false, error: error.message };
    }
});
electron_1.ipcMain.handle('enable-auto-launch', async () => {
    try {
        await autoLauncher.enable();
        return { success: true, message: 'Auto-launch enabled successfully' };
    }
    catch (error) {
        console.error('Error enabling auto-launch:', error);
        return { success: false, error: error.message };
    }
});
electron_1.ipcMain.handle('disable-auto-launch', async () => {
    try {
        await autoLauncher.disable();
        return { success: true, message: 'Auto-launch disabled successfully' };
    }
    catch (error) {
        console.error('Error disabling auto-launch:', error);
        return { success: false, error: error.message };
    }
});
electron_1.ipcMain.handle('check-app-installed', async (event, { executable, paths }) => {
    try {
        console.log(`Checking installation for: ${executable}`);
        // Check each path
        for (const execPath of paths) {
            if (fs.existsSync(execPath)) {
                console.log(`Found ${executable} at: ${execPath}`);
                // Try to get version info (optional)
                let version = 'Terdeteksi';
                try {
                    // You can add version detection logic here if needed
                    const stats = fs.statSync(execPath);
                    version = `Terdeteksi (${stats.mtime.toLocaleDateString()})`;
                }
                catch (versionError) {
                    // Version detection failed, use default
                }
                return {
                    found: true,
                    path: execPath,
                    version: version
                };
            }
        }
        console.log(`${executable} not found in any provided paths`);
        return {
            found: false,
            path: null,
            version: null
        };
    }
    catch (error) {
        console.error(`Error checking ${executable}:`, error);
        return {
            found: false,
            path: null,
            version: null,
            error: error.message
        };
    }
});
// App event handlers
electron_1.app.whenReady().then(() => {
    createWindow();
    // Enable auto-launch by default in production
    if (!isDev) {
        autoLauncher.isEnabled().then((isEnabled) => {
            if (!isEnabled) {
                console.log('Enabling auto-launch for production...');
                autoLauncher.enable().catch((error) => {
                    console.error('Failed to enable auto-launch:', error);
                });
            }
        });
    }
});
electron_1.app.on('window-all-closed', () => {
    // On macOS, keep app running even when all windows are closed
    if (process.platform === 'darwin') {
        return;
    }
    // On Windows/Linux, keep running in tray
    if (tray) {
        return;
    }
    electron_1.app.quit();
});
electron_1.app.on('activate', () => {
    if (electron_1.BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
    else if (mainWindow) {
        mainWindow.show();
        mainWindow.focus();
    }
});
// Prevent multiple instances
const gotTheLock = electron_1.app.requestSingleInstanceLock();
if (!gotTheLock) {
    electron_1.app.quit();
}
else {
    electron_1.app.on('second-instance', () => {
        // Someone tried to run a second instance, focus our window instead
        if (mainWindow) {
            if (mainWindow.isMinimized())
                mainWindow.restore();
            mainWindow.focus();
            mainWindow.show();
        }
    });
}
// Handle app being launched at startup
electron_1.app.on('ready', () => {
    // Check if app was launched at startup
    const wasOpenedAtLogin = electron_1.app.getLoginItemSettings().wasOpenedAtLogin;
    if (wasOpenedAtLogin) {
        console.log('App was launched at startup');
        // You can add startup-specific behavior here
        // For example, start minimized to tray
        if (mainWindow) {
            mainWindow.hide();
        }
    }
});
//# sourceMappingURL=main.js.map