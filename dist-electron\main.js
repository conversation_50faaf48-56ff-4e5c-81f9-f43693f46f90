"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const child_process_1 = require("child_process");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const isDev = process.env.NODE_ENV === 'development';
// Common installation paths for remap applications (now empty - will use custom paths)
const commonPaths = {};
function createWindow() {
    // Create the browser window
    const mainWindow = new electron_1.BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        },
        icon: path.join(__dirname, '../public/favicon.ico'),
        title: 'PAS Auto Control Hub',
        show: false
    });
    // Load the app
    if (isDev) {
        mainWindow.loadURL('http://localhost:8080');
        // Open DevTools in development
        mainWindow.webContents.openDevTools();
    }
    else {
        mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
    }
    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
    });
    // Handle window closed
    mainWindow.on('closed', () => {
        electron_1.app.quit();
    });
}
// Function to find executable
function findExecutable(executableName, customPaths = []) {
    const paths = customPaths.length > 0 ? customPaths : (commonPaths[executableName] || []);
    for (const execPath of paths) {
        if (fs.existsSync(execPath)) {
            return execPath;
        }
    }
    return null;
}
// IPC handlers
electron_1.ipcMain.handle('launch-app', async (event, { executable, name, paths }) => {
    try {
        console.log(`Attempting to launch: ${name} (${executable})`);
        // Find the executable
        const executablePath = findExecutable(executable, paths);
        if (!executablePath) {
            return {
                success: false,
                error: `Executable ${executable} not found in provided paths`
            };
        }
        // Launch the application
        const child = (0, child_process_1.spawn)(executablePath, [], {
            detached: true,
            stdio: 'ignore'
        });
        child.unref();
        console.log(`Successfully launched: ${name} from ${executablePath}`);
        return {
            success: true,
            message: `Successfully launched ${name}`,
            path: executablePath
        };
    }
    catch (error) {
        console.error(`Error launching ${name}:`, error);
        return {
            success: false,
            error: error.message
        };
    }
});
electron_1.ipcMain.handle('start-app', async (event, { executable, name }) => {
    return new Promise((resolve) => {
        console.log(`Attempting to start: ${name} using Windows start command`);
        // Try using Windows start command
        (0, child_process_1.exec)(`start "" "${executable}"`, (error, stdout, stderr) => {
            if (error) {
                console.error(`Error starting ${name}:`, error);
                resolve({
                    success: false,
                    error: error.message
                });
                return;
            }
            console.log(`Successfully started: ${name} using start command`);
            resolve({
                success: true,
                message: `Successfully started ${name} using Windows start command`
            });
        });
    });
});
electron_1.ipcMain.handle('open-external', async (event, url) => {
    electron_1.shell.openExternal(url);
});
electron_1.ipcMain.handle('check-app-installed', async (event, { executable, paths }) => {
    try {
        console.log(`Checking installation for: ${executable}`);
        // Check each path
        for (const execPath of paths) {
            if (fs.existsSync(execPath)) {
                console.log(`Found ${executable} at: ${execPath}`);
                // Try to get version info (optional)
                let version = 'Terdeteksi';
                try {
                    // You can add version detection logic here if needed
                    const stats = fs.statSync(execPath);
                    version = `Terdeteksi (${stats.mtime.toLocaleDateString()})`;
                }
                catch (versionError) {
                    // Version detection failed, use default
                }
                return {
                    found: true,
                    path: execPath,
                    version: version
                };
            }
        }
        console.log(`${executable} not found in any provided paths`);
        return {
            found: false,
            path: null,
            version: null
        };
    }
    catch (error) {
        console.error(`Error checking ${executable}:`, error);
        return {
            found: false,
            path: null,
            version: null,
            error: error.message
        };
    }
});
// App event handlers
electron_1.app.whenReady().then(createWindow);
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        electron_1.app.quit();
    }
});
electron_1.app.on('activate', () => {
    if (electron_1.BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});
//# sourceMappingURL=main.js.map