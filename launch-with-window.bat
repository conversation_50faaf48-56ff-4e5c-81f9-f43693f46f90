@echo off
echo ========================================
echo PAS Auto Control Hub - Window Launcher
echo ========================================

:: Set working directory to script location
cd /d "%~dp0"

:: Set environment for production
set NODE_ENV=production

:: Kill any existing instances first
echo [INFO] Checking for existing instances...
taskkill /f /im electron.exe >nul 2>&1
timeout /t 2 /nobreak > nul

:: Check if dist folder exists (production build)
if exist "dist\index.html" (
  :: Production mode - run electron with built files
  echo [INFO] Running in production mode...
  echo [INFO] Loading from built files...
  
  :: Start Electron with window visible
  echo [INFO] Starting PAS Auto Control Hub with login window...
  start "PAS Auto Control Hub" /wait /normal npx electron .
  
  echo [SUCCESS] PAS Auto Control Hub started successfully!
  echo [INFO] Login window should be visible now.
  
) else (
  :: Build first if dist doesn't exist
  echo [INFO] Building application for first-time startup...
  call npm run build
  
  if exist "dist\index.html" (
    echo [SUCCESS] Build completed successfully!
    echo [INFO] Starting PAS Auto Control Hub with login window...
    start "PAS Auto Control Hub" /wait /normal npx electron .
  ) else (
    echo [ERROR] Build failed! Please run 'npm run build' manually.
    pause
    exit /b 1
  )
)

:: Show completion message
echo.
echo ========================================
echo PAS Auto Control Hub is now running!
echo ========================================
echo.
echo If login window is not visible:
echo 1. Check taskbar for PAS Auto Control Hub
echo 2. Look for PAS icon in system tray
echo 3. Press Alt+Tab to find the window
echo 4. Try clicking the taskbar icon
echo.

:: Wait for user confirmation
echo Press any key to close this launcher...
pause > nul
exit
