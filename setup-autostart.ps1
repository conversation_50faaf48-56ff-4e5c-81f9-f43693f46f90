# PAS Auto Control Hub - Auto-Start Setup (PowerShell)
# Run as Administrator for Registry method

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("enable", "disable", "status", "menu")]
    [string]$Action = "menu"
)

$AppName = "PAS Auto Control Hub"
$AppPath = Join-Path $PSScriptRoot "run-electron.bat"
$RegistryPath = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Run"
$StartupFolder = [Environment]::GetFolderPath("Startup")

function Show-Menu {
    Clear-Host
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "PAS Auto Control Hub - Auto-Start Setup" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "App Path: $AppPath" -ForegroundColor Gray
    Write-Host "Registry: $RegistryPath" -ForegroundColor Gray
    Write-Host "Startup Folder: $StartupFolder" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Pilih metode auto-start:" -ForegroundColor Yellow
    Write-Host "1. Registry (Recommended) - Aktifkan"
    Write-Host "2. Registry (Recommended) - Nonaktifkan"
    Write-Host "3. Startup Folder - Aktifkan"
    Write-Host "4. Startup Folder - Nonaktifkan"
    Write-Host "5. Cek Status"
    Write-Host "6. Keluar"
    Write-Host ""
}

function Enable-RegistryAutoStart {
    try {
        Set-ItemProperty -Path $RegistryPath -Name $AppName -Value $AppPath -Force
        Write-Host "✅ Auto-start berhasil diaktifkan via Registry!" -ForegroundColor Green
        Write-Host "Aplikasi akan berjalan otomatis saat Windows startup." -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Gagal mengaktifkan auto-start via Registry!" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Disable-RegistryAutoStart {
    try {
        Remove-ItemProperty -Path $RegistryPath -Name $AppName -ErrorAction SilentlyContinue
        Write-Host "✅ Auto-start berhasil dinonaktifkan via Registry!" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️  Auto-start mungkin sudah tidak aktif." -ForegroundColor Yellow
    }
}

function Enable-StartupFolderAutoStart {
    try {
        $ShortcutPath = Join-Path $StartupFolder "$AppName.bat"
        Copy-Item $AppPath $ShortcutPath -Force
        Write-Host "✅ Auto-start berhasil diaktifkan via Startup Folder!" -ForegroundColor Green
        Write-Host "File: $ShortcutPath" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Gagal mengaktifkan auto-start via Startup Folder!" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Disable-StartupFolderAutoStart {
    try {
        $ShortcutPath = Join-Path $StartupFolder "$AppName.bat"
        Remove-Item $ShortcutPath -ErrorAction SilentlyContinue
        Write-Host "✅ Auto-start berhasil dinonaktifkan via Startup Folder!" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️  Auto-start mungkin sudah tidak aktif." -ForegroundColor Yellow
    }
}

function Get-AutoStartStatus {
    Write-Host "Mengecek status auto-start..." -ForegroundColor Yellow
    Write-Host ""
    
    # Check Registry
    $RegistryValue = Get-ItemProperty -Path $RegistryPath -Name $AppName -ErrorAction SilentlyContinue
    if ($RegistryValue) {
        Write-Host "✅ Registry Auto-start: AKTIF" -ForegroundColor Green
        Write-Host "   Path: $($RegistryValue.$AppName)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Registry Auto-start: TIDAK AKTIF" -ForegroundColor Red
    }
    
    # Check Startup Folder
    $ShortcutPath = Join-Path $StartupFolder "$AppName.bat"
    if (Test-Path $ShortcutPath) {
        Write-Host "✅ Startup Folder Auto-start: AKTIF" -ForegroundColor Green
        Write-Host "   File: $ShortcutPath" -ForegroundColor Gray
    } else {
        Write-Host "❌ Startup Folder Auto-start: TIDAK AKTIF" -ForegroundColor Red
    }
}

# Main execution
switch ($Action) {
    "enable" {
        Enable-RegistryAutoStart
        return
    }
    "disable" {
        Disable-RegistryAutoStart
        Disable-StartupFolderAutoStart
        return
    }
    "status" {
        Get-AutoStartStatus
        return
    }
    "menu" {
        do {
            Show-Menu
            $choice = Read-Host "Masukkan pilihan (1-6)"
            
            switch ($choice) {
                "1" { 
                    Enable-RegistryAutoStart
                    Write-Host ""
                    Read-Host "Tekan Enter untuk melanjutkan"
                }
                "2" { 
                    Disable-RegistryAutoStart
                    Write-Host ""
                    Read-Host "Tekan Enter untuk melanjutkan"
                }
                "3" { 
                    Enable-StartupFolderAutoStart
                    Write-Host ""
                    Read-Host "Tekan Enter untuk melanjutkan"
                }
                "4" { 
                    Disable-StartupFolderAutoStart
                    Write-Host ""
                    Read-Host "Tekan Enter untuk melanjutkan"
                }
                "5" { 
                    Get-AutoStartStatus
                    Write-Host ""
                    Read-Host "Tekan Enter untuk melanjutkan"
                }
                "6" { 
                    Write-Host "Terima kasih telah menggunakan PAS Auto Control Hub!" -ForegroundColor Cyan
                    return
                }
                default { 
                    Write-Host "Pilihan tidak valid!" -ForegroundColor Red
                    Start-Sleep 2
                }
            }
        } while ($choice -ne "6")
    }
}
