{"version": 3, "file": "preload.js", "sourceRoot": "", "sources": ["../electron/preload.ts"], "names": [], "mappings": ";;AAAA,uCAAsD;AAEtD,kEAAkE;AAClE,qDAAqD;AACrD,wBAAa,CAAC,iBAAiB,CAAC,aAAa,EAAE;IAC7C,SAAS,EAAE,CAAC,UAAkB,EAAE,IAAY,EAAE,EAAE,CAC9C,sBAAW,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;IAExD,QAAQ,EAAE,CAAC,UAAkB,EAAE,IAAY,EAAE,EAAE,CAC7C,sBAAW,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;IAEvD,YAAY,EAAE,CAAC,GAAW,EAAE,EAAE,CAC5B,sBAAW,CAAC,MAAM,CAAC,eAAe,EAAE,GAAG,CAAC;IAE1C,gBAAgB;IAChB,QAAQ,EAAE,OAAO,CAAC,QAAQ;IAC1B,UAAU,EAAE,IAAI;CACjB,CAAC,CAAC"}