{"version": 3, "file": "preload.js", "sourceRoot": "", "sources": ["../electron/preload.ts"], "names": [], "mappings": ";;AAAA,uCAAsD;AAEtD,kEAAkE;AAClE,qDAAqD;AACrD,wBAAa,CAAC,iBAAiB,CAAC,aAAa,EAAE;IAC7C,SAAS,EAAE,CAAC,UAAkB,EAAE,IAAY,EAAE,KAAe,EAAE,EAAE,CAC/D,sBAAW,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IAE/D,QAAQ,EAAE,CAAC,UAAkB,EAAE,IAAY,EAAE,EAAE,CAC7C,sBAAW,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;IAEvD,YAAY,EAAE,CAAC,GAAW,EAAE,EAAE,CAC5B,sBAAW,CAAC,MAAM,CAAC,eAAe,EAAE,GAAG,CAAC;IAE1C,iBAAiB,EAAE,CAAC,UAAkB,EAAE,KAAe,EAAE,EAAE,CACzD,sBAAW,CAAC,MAAM,CAAC,qBAAqB,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;IAElE,gBAAgB;IAChB,QAAQ,EAAE,OAAO,CAAC,QAAQ;IAC1B,UAAU,EAAE,IAAI;CACjB,CAAC,CAAC"}