import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export const useActivityLogger = () => {
  const logActivity = async (
    activityType: string,
    description: string,
    appName?: string
  ) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) return;

      const sessionId = localStorage.getItem('session_id') || generateSessionId();
      
      await supabase.from('user_activity_logs').insert({
        user_id: user.id,
        activity_type: activityType,
        activity_description: description,
        app_name: appName,
        session_id: sessionId,
        ip_address: await getClientIP(),
        user_agent: navigator.userAgent
      });

      // Update session last activity
      await supabase
        .from('user_sessions')
        .upsert({
          user_id: user.id,
          session_id: sessionId,
          status: 'active',
          last_activity: new Date().toISOString(),
          ip_address: await getClientIP(),
          user_agent: navigator.userAgent
        }, {
          onConflict: 'session_id'
        });

    } catch (error) {
      console.error('Error logging activity:', error);
    }
  };

  const startSession = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) return;

      const sessionId = generateSessionId();
      localStorage.setItem('session_id', sessionId);

      await supabase.from('user_sessions').insert({
        user_id: user.id,
        session_id: sessionId,
        status: 'active',
        last_activity: new Date().toISOString(),
        ip_address: await getClientIP(),
        user_agent: navigator.userAgent
      });

      await logActivity('login', 'User logged in to the system');

    } catch (error) {
      console.error('Error starting session:', error);
    }
  };

  const endSession = async () => {
    try {
      const sessionId = localStorage.getItem('session_id');
      
      if (!sessionId) return;

      await logActivity('logout', 'User logged out of the system');

      await supabase
        .from('user_sessions')
        .update({
          status: 'inactive',
          ended_at: new Date().toISOString()
        })
        .eq('session_id', sessionId);

      localStorage.removeItem('session_id');

    } catch (error) {
      console.error('Error ending session:', error);
    }
  };

  const generateSessionId = () => {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const getClientIP = async () => {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch (error) {
      return '127.0.0.1'; // fallback
    }
  };

  // Auto-update session activity every 5 minutes
  useEffect(() => {
    const interval = setInterval(async () => {
      const sessionId = localStorage.getItem('session_id');
      if (sessionId) {
        try {
          await supabase
            .from('user_sessions')
            .update({
              last_activity: new Date().toISOString()
            })
            .eq('session_id', sessionId);
        } catch (error) {
          console.error('Error updating session activity:', error);
        }
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, []);

  return {
    logActivity,
    startSession,
    endSession
  };
};