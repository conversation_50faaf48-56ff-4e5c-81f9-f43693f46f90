const { app, BrowserWindow, ipcMain, shell, session } = require('electron');
const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// Auto-startup functionality
const AutoLaunch = require('auto-launch');

// Security: Disable navigation to prevent phishing attacks
app.on('web-contents-created', (event, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    // Only allow localhost in dev mode
    if (isDev && parsedUrl.host !== 'localhost') {
      event.preventDefault();
    }
  });

  // Prevent creating new windows
  contents.setWindowOpenHandler(({ url }) => {
    // Open URLs in the default browser instead
    shell.openExternal(url);
    return { action: 'deny' };
  });
});

const isDev = process.env.NODE_ENV === 'development';

// Auto-launch configuration
const startupScriptPath = path.join(__dirname, '../startup-pas-hub.bat');
const autoLauncher = new AutoLaunch({
  name: 'PAS Auto Control Hub',
  path: startupScriptPath,
  isHidden: false, // Set to true if you want it to start minimized
});

// Common installation paths for remap applications (now empty - will use custom paths)
const commonPaths = {};

function createWindow() {
  // Create the browser window
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.cjs'),
      sandbox: true,
      webSecurity: true,
      allowRunningInsecureContent: false,
      enableRemoteModule: false
    },
    icon: path.join(__dirname, 'favicon.ico'),
    title: 'PAS Auto Control Hub',
    show: false
  });

  // Set Content Security Policy
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          isDev
            ? "default-src 'self' 'unsafe-inline' 'unsafe-eval' http://localhost:* ws://localhost:* data: blob:; img-src 'self' data: blob: https:; font-src 'self' data:;"
            : "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: https:; font-src 'self' data:; connect-src 'self' https:;"
        ]
      }
    });
  });

  // Load the app
  if (isDev) {
    // Development mode - try to connect to dev server
    const devServerUrl = 'http://localhost:8081';

    // Check if dev server is running
    mainWindow.loadURL(devServerUrl).catch(() => {
      // If dev server is not running, show error page
      const errorHtml = `
        <html>
          <head><title>PAS Auto Control Hub - Dev Server Required</title></head>
          <body style="font-family: Arial; padding: 40px; text-align: center; background: #1a1a1a; color: white;">
            <h1>🚀 PAS Auto Control Hub</h1>
            <h2 style="color: #ff6b6b;">Development Server Required</h2>
            <p>Please start the development server first:</p>
            <code style="background: #333; padding: 10px; border-radius: 5px; display: block; margin: 20px 0;">npm run dev</code>
            <p>Then restart this application.</p>
            <button onclick="location.reload()" style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">Retry</button>
          </body>
        </html>
      `;
      mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(errorHtml)}`);
    });

    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    // Production mode - load built files
    const indexPath = path.join(__dirname, '../dist/index.html');
    if (fs.existsSync(indexPath)) {
      mainWindow.loadFile(indexPath);
    } else {
      // If dist folder doesn't exist, show build instruction
      const buildHtml = `
        <html>
          <head><title>PAS Auto Control Hub - Build Required</title></head>
          <body style="font-family: Arial; padding: 40px; text-align: center; background: #1a1a1a; color: white;">
            <h1>🚀 PAS Auto Control Hub</h1>
            <h2 style="color: #ffa500;">Build Required</h2>
            <p>Please build the application first:</p>
            <code style="background: #333; padding: 10px; border-radius: 5px; display: block; margin: 20px 0;">npm run build</code>
            <p>Then restart this application.</p>
            <button onclick="location.reload()" style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">Retry</button>
          </body>
        </html>
      `;
      mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(buildHtml)}`);
    }
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    app.quit();
  });
}

// Function to find executable
function findExecutable(executableName, customPaths = []) {
  const paths = customPaths.length > 0 ? customPaths : (commonPaths[executableName] || []);

  for (const execPath of paths) {
    if (fs.existsSync(execPath)) {
      return execPath;
    }
  }

  return null;
}

// IPC handlers
ipcMain.handle('launch-app', async (event, { executable, name, paths }) => {
  try {
    console.log(`Attempting to launch: ${name} (${executable})`);
    console.log(`Checking paths:`, paths);

    // Find the executable
    const executablePath = findExecutable(executable, paths || []);

    if (!executablePath) {
      return {
        success: false,
        error: `Executable ${executable} not found in provided paths`
      };
    }

    // Launch the application
    const child = spawn(executablePath, [], {
      detached: true,
      stdio: 'ignore'
    });

    child.unref();

    console.log(`Successfully launched: ${name} from ${executablePath}`);

    return {
      success: true,
      message: `Successfully launched ${name}`,
      path: executablePath
    };
  } catch (error) {
    console.error(`Error launching ${name}:`, error);
    return {
      success: false,
      error: error.message
    };
  }
});

ipcMain.handle('start-app', async (event, { executable, name }) => {
  return new Promise((resolve) => {
    console.log(`Attempting to start: ${name} using Windows start command`);
    
    // Try using Windows start command
    exec(`start "" "${executable}"`, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error starting ${name}:`, error);
        resolve({
          success: false,
          error: error.message
        });
        return;
      }
      
      console.log(`Successfully started: ${name} using start command`);
      
      resolve({
        success: true,
        message: `Successfully started ${name} using Windows start command`
      });
    });
  });
});

ipcMain.handle('open-external', async (event, url) => {
  shell.openExternal(url);
});

ipcMain.handle('check-app-installed', async (event, { executable, paths }) => {
  try {
    console.log(`Checking installation for: ${executable}`);

    // Check each path
    for (const execPath of paths) {
      if (fs.existsSync(execPath)) {
        console.log(`Found ${executable} at: ${execPath}`);

        // Try to get version info (optional)
        let version = 'Terdeteksi';
        try {
          // You can add version detection logic here if needed
          const stats = fs.statSync(execPath);
          version = `Terdeteksi (${stats.mtime.toLocaleDateString()})`;
        } catch (versionError) {
          // Version detection failed, use default
        }

        return {
          found: true,
          path: execPath,
          version: version
        };
      }
    }

    console.log(`${executable} not found in any provided paths`);
    return {
      found: false,
      path: null,
      version: null
    };
  } catch (error) {
    console.error(`Error checking ${executable}:`, error);
    return {
      found: false,
      path: null,
      version: null,
      error: error.message
    };
  }
});

// Auto-launch handlers
ipcMain.handle('get-auto-launch-status', async () => {
  try {
    const isEnabled = await autoLauncher.isEnabled();
    return { enabled: isEnabled };
  } catch (error) {
    console.error('Error checking auto-launch status:', error);
    return { enabled: false, error: error.message };
  }
});

ipcMain.handle('enable-auto-launch', async () => {
  try {
    await autoLauncher.enable();
    return { success: true, message: 'Auto-launch enabled successfully' };
  } catch (error) {
    console.error('Error enabling auto-launch:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('disable-auto-launch', async () => {
  try {
    await autoLauncher.disable();
    return { success: true, message: 'Auto-launch disabled successfully' };
  } catch (error) {
    console.error('Error disabling auto-launch:', error);
    return { success: false, error: error.message };
  }
});

// App event handlers
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
