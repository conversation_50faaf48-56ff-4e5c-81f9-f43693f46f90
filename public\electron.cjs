const { app, BrowserWindow, ipcMain, shell, session } = require('electron');
const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// Auto-startup functionality
const AutoLaunch = require('auto-launch');

// Security: Disable navigation to prevent phishing attacks
app.on('web-contents-created', (event, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    // Only allow localhost in dev mode
    if (isDev && parsedUrl.host !== 'localhost') {
      event.preventDefault();
    }
  });

  // Prevent creating new windows
  contents.setWindowOpenHandler(({ url }) => {
    // Open URLs in the default browser instead
    shell.openExternal(url);
    return { action: 'deny' };
  });
});

const isDev = process.env.NODE_ENV === 'development';

// Auto-launch configuration
const startupScriptPath = path.join(__dirname, '../startup-pas-hub.bat');
const autoLauncher = new AutoLaunch({
  name: 'PAS Auto Control Hub',
  path: startupScriptPath,
  isHidden: false, // Set to true if you want it to start minimized
});

// Common installation paths for remap applications
const commonPaths = {
  'techstream.exe': [
    'C:\\Program Files\\Toyota\\Techstream\\techstream.exe',
    'C:\\Program Files (x86)\\Toyota\\Techstream\\techstream.exe',
    'C:\\Toyota\\Techstream\\techstream.exe',
    'D:\\Toyota\\Techstream\\techstream.exe',
    'C:\\Program Files\\Techstream\\techstream.exe',
    'C:\\Program Files (x86)\\Techstream\\techstream.exe',
    'C:\\Techstream\\techstream.exe',
    'D:\\Techstream\\techstream.exe'
  ],
  'TechstreamV2.exe': [
    'C:\\Program Files\\Toyota\\TechstreamV2\\TechstreamV2.exe',
    'C:\\Program Files (x86)\\Toyota\\TechstreamV2\\TechstreamV2.exe',
    'C:\\Toyota\\TechstreamV2\\TechstreamV2.exe',
    'D:\\Toyota\\TechstreamV2\\TechstreamV2.exe'
  ]
};

function createWindow() {
  // Create the browser window
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.cjs'),
      sandbox: true,
      webSecurity: true,
      allowRunningInsecureContent: false,
      enableRemoteModule: false
    },
    icon: path.join(__dirname, 'favicon.ico'),
    title: 'PAS Auto Control Hub',
    show: false
  });

  // Set Content Security Policy
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          isDev
            ? "default-src 'self' 'unsafe-inline' 'unsafe-eval' http://localhost:* ws://localhost:* data: blob:; img-src 'self' data: blob: https:; font-src 'self' data:;"
            : "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: https:; font-src 'self' data:; connect-src 'self' https:;"
        ]
      }
    });
  });

  // Load the app
  if (isDev) {
    // Development mode - try to connect to dev server
    const devServerUrl = 'http://localhost:8080';

    console.log('Development mode - loading from:', devServerUrl);
    mainWindow.loadURL(devServerUrl).catch(() => {
      // If dev server is not running, show error page
      const errorHtml = `
        <html>
          <head><title>PAS Auto Control Hub - Dev Server Required</title></head>
          <body style="font-family: Arial; padding: 40px; text-align: center; background: #1a1a1a; color: white;">
            <h1>🚀 PAS Auto Control Hub</h1>
            <h2 style="color: #ff6b6b;">Development Server Required</h2>
            <p>Please start the development server first:</p>
            <code style="background: #333; padding: 10px; border-radius: 5px; display: block; margin: 20px 0;">npm run dev</code>
            <p>Then restart this application.</p>
            <button onclick="location.reload()" style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">Retry</button>
          </body>
        </html>
      `;
      mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(errorHtml)}`);
    });

    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    // Production mode - load built files
    const indexPath = path.join(__dirname, '../dist/index.html');
    console.log('Production mode - loading from:', indexPath);
    console.log('File exists:', fs.existsSync(indexPath));

    if (fs.existsSync(indexPath)) {
      console.log('Loading index.html file...');
      mainWindow.loadFile(indexPath);

      // Add error handling for loading
      mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
        console.error('Failed to load:', errorCode, errorDescription);
        const errorHtml = `
          <html>
            <head><title>PAS Auto Control Hub - Loading Error</title></head>
            <body style="font-family: Arial; padding: 40px; text-align: center; background: #1a1a1a; color: white;">
              <h1>🚀 PAS Auto Control Hub</h1>
              <h2 style="color: #ff6b6b;">Loading Error</h2>
              <p>Error Code: ${errorCode}</p>
              <p>Description: ${errorDescription}</p>
              <p>File Path: ${indexPath}</p>
              <button onclick="location.reload()" style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">Retry</button>
            </body>
          </html>
        `;
        mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(errorHtml)}`);
      });

      // Success handler
      mainWindow.webContents.on('did-finish-load', () => {
        console.log('Successfully loaded application!');
      });

    } else {
      // If dist folder doesn't exist, show build instruction
      const buildHtml = `
        <html>
          <head><title>PAS Auto Control Hub - Build Required</title></head>
          <body style="font-family: Arial; padding: 40px; text-align: center; background: #1a1a1a; color: white;">
            <h1>🚀 PAS Auto Control Hub</h1>
            <h2 style="color: #ffa500;">Build Required</h2>
            <p>Please build the application first:</p>
            <code style="background: #333; padding: 10px; border-radius: 5px; display: block; margin: 20px 0;">npm run build</code>
            <p>Expected file: ${indexPath}</p>
            <p>Current directory: ${__dirname}</p>
            <button onclick="location.reload()" style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">Retry</button>
          </body>
        </html>
      `;
      mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(buildHtml)}`);
    }
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    app.quit();
  });
}

// Function to find executable
function findExecutable(executableName, customPaths = []) {
  const paths = customPaths.length > 0 ? customPaths : (commonPaths[executableName] || []);

  console.log(`[DEBUG] Finding executable: ${executableName}`);
  console.log(`[DEBUG] Custom paths provided: ${customPaths.length}`);
  console.log(`[DEBUG] Paths to check:`, paths);

  for (const execPath of paths) {
    console.log(`[DEBUG] Checking path: ${execPath}`);
    console.log(`[DEBUG] Path exists: ${fs.existsSync(execPath)}`);

    if (fs.existsSync(execPath)) {
      console.log(`[DEBUG] Found executable at: ${execPath}`);
      return execPath;
    }
  }

  console.log(`[DEBUG] Executable not found in any path`);
  return null;
}

// IPC handlers
ipcMain.handle('launch-app', async (event, { executable, name, paths }) => {
  try {
    console.log(`[LAUNCH] Attempting to launch: ${name} (${executable})`);
    console.log(`[LAUNCH] Provided paths:`, paths);

    // Find the executable
    const executablePath = findExecutable(executable, paths || []);

    if (!executablePath) {
      const errorMsg = `Executable ${executable} not found in any of the provided paths: ${JSON.stringify(paths)}`;
      console.error(`[LAUNCH ERROR] ${errorMsg}`);
      return {
        success: false,
        error: errorMsg,
        checkedPaths: paths
      };
    }

    console.log(`[LAUNCH] Found executable at: ${executablePath}`);

    // Try different launch methods
    let launchSuccess = false;
    let launchError = null;

    // Method 1: Direct spawn
    try {
      console.log(`[LAUNCH] Method 1: Direct spawn`);
      const child = spawn(executablePath, [], {
        detached: true,
        stdio: 'ignore',
        shell: false
      });

      child.unref();

      // Wait a bit to see if process starts successfully
      await new Promise(resolve => setTimeout(resolve, 1000));

      launchSuccess = true;
      console.log(`[LAUNCH SUCCESS] Method 1: Successfully launched ${name}`);
    } catch (spawnError) {
      console.log(`[LAUNCH] Method 1 failed:`, spawnError.message);
      launchError = spawnError;

      // Method 2: Shell execution
      try {
        console.log(`[LAUNCH] Method 2: Shell execution`);
        const { exec } = require('child_process');

        exec(`"${executablePath}"`, (error, stdout, stderr) => {
          if (error) {
            console.error(`[LAUNCH] Method 2 error:`, error);
          } else {
            console.log(`[LAUNCH SUCCESS] Method 2: Successfully launched ${name}`);
          }
        });

        launchSuccess = true;
      } catch (execError) {
        console.log(`[LAUNCH] Method 2 failed:`, execError.message);

        // Method 3: Windows start command
        try {
          console.log(`[LAUNCH] Method 3: Windows start command`);
          exec(`start "" "${executablePath}"`, (error, stdout, stderr) => {
            if (error) {
              console.error(`[LAUNCH] Method 3 error:`, error);
            } else {
              console.log(`[LAUNCH SUCCESS] Method 3: Successfully launched ${name}`);
            }
          });

          launchSuccess = true;
        } catch (startError) {
          console.log(`[LAUNCH] Method 3 failed:`, startError.message);
          launchError = startError;
        }
      }
    }

    if (launchSuccess) {
      return {
        success: true,
        message: `Successfully launched ${name}`,
        path: executablePath
      };
    } else {
      return {
        success: false,
        error: `Failed to launch ${name}: ${launchError?.message || 'Unknown error'}`,
        path: executablePath
      };
    }

  } catch (error) {
    console.error(`[LAUNCH ERROR] Error launching ${name}:`, error);
    return {
      success: false,
      error: error.message,
      stack: error.stack
    };
  }
});

ipcMain.handle('start-app', async (event, { executable, name }) => {
  return new Promise((resolve) => {
    console.log(`Attempting to start: ${name} using Windows start command`);
    
    // Try using Windows start command
    exec(`start "" "${executable}"`, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error starting ${name}:`, error);
        resolve({
          success: false,
          error: error.message
        });
        return;
      }
      
      console.log(`Successfully started: ${name} using start command`);
      
      resolve({
        success: true,
        message: `Successfully started ${name} using Windows start command`
      });
    });
  });
});

ipcMain.handle('open-external', async (event, url) => {
  shell.openExternal(url);
});

// Debug handler untuk Techstream
ipcMain.handle('debug-techstream', async (event) => {
  console.log('[DEBUG] Checking Techstream installation...');

  const techstreamPaths = [
    'C:\\Program Files\\Toyota\\Techstream\\techstream.exe',
    'C:\\Program Files (x86)\\Toyota\\Techstream\\techstream.exe',
    'C:\\Toyota\\Techstream\\techstream.exe',
    'D:\\Toyota\\Techstream\\techstream.exe',
    'C:\\Program Files\\Techstream\\techstream.exe',
    'C:\\Program Files (x86)\\Techstream\\techstream.exe',
    'C:\\Techstream\\techstream.exe',
    'D:\\Techstream\\techstream.exe'
  ];

  const results = [];

  for (const path of techstreamPaths) {
    const exists = fs.existsSync(path);
    results.push({
      path: path,
      exists: exists,
      stats: exists ? fs.statSync(path) : null
    });
    console.log(`[DEBUG] ${path}: ${exists ? 'EXISTS' : 'NOT FOUND'}`);
  }

  return {
    paths: results,
    foundPaths: results.filter(r => r.exists).map(r => r.path)
  };
});

ipcMain.handle('check-app-installed', async (event, { executable, paths }) => {
  try {
    console.log(`Checking installation for: ${executable}`);

    // Check each path
    for (const execPath of paths) {
      if (fs.existsSync(execPath)) {
        console.log(`Found ${executable} at: ${execPath}`);

        // Try to get version info (optional)
        let version = 'Terdeteksi';
        try {
          // You can add version detection logic here if needed
          const stats = fs.statSync(execPath);
          version = `Terdeteksi (${stats.mtime.toLocaleDateString()})`;
        } catch (versionError) {
          // Version detection failed, use default
        }

        return {
          found: true,
          path: execPath,
          version: version
        };
      }
    }

    console.log(`${executable} not found in any provided paths`);
    return {
      found: false,
      path: null,
      version: null
    };
  } catch (error) {
    console.error(`Error checking ${executable}:`, error);
    return {
      found: false,
      path: null,
      version: null,
      error: error.message
    };
  }
});

// Auto-launch handlers
ipcMain.handle('get-auto-launch-status', async () => {
  try {
    const isEnabled = await autoLauncher.isEnabled();
    return { enabled: isEnabled };
  } catch (error) {
    console.error('Error checking auto-launch status:', error);
    return { enabled: false, error: error.message };
  }
});

ipcMain.handle('enable-auto-launch', async () => {
  try {
    await autoLauncher.enable();
    return { success: true, message: 'Auto-launch enabled successfully' };
  } catch (error) {
    console.error('Error enabling auto-launch:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('disable-auto-launch', async () => {
  try {
    await autoLauncher.disable();
    return { success: true, message: 'Auto-launch disabled successfully' };
  } catch (error) {
    console.error('Error disabling auto-launch:', error);
    return { success: false, error: error.message };
  }
});

// App event handlers
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
