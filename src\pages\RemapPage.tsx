import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft,
  Monitor,
  Settings,
  Zap,
  Gauge,
  FileText,
  BarChart3,
  Wrench,
  ExternalLink,
  Play
} from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import pasLogo from "@/assets/pas-logo.png";

declare global {
  interface Window {
    electronAPI?: {
      launchApp: (executable: string) => void;
    };
  }
}

export default function RemapPage() {
  const navigate = useNavigate();
  const { appName } = useParams<{ appName: string }>();

  const installedApps = [
    {
      id: 1,
      name: "WinOLS Professional",
      description: "Software tuning ECU profesional",
      version: "v5.26",
      icon: Zap,
      status: "installed",
      executable: "winols.exe",
      category: "Tuning ECU"
    },
    {
      id: 2,
      name: "KESS V2",
      description: "Alat pemrograman ECU master",
      version: "v5.017",
      icon: Monitor,
      status: "installed", 
      executable: "kessv2.exe",
      category: "Pemrograman"
    },
    {
      id: 3,
      name: "KTAG Master",
      description: "Pemrograman ECU via JTAG/BDM",
      version: "v7.020",
      icon: Settings,
      status: "installed",
      executable: "ktag.exe", 
      category: "Programming"
    },
    {
      id: 4,
      name: "AutoTuner Tool",
      description: "Solusi tuning profesional",
      version: "v4.2",
      icon: Gauge,
      status: "installed",
      executable: "autotuner.exe",
      category: "Tuning"
    },
    {
      id: 5,
      name: "PCMTuner",
      description: "Pembacaan dan penulisan PCM/ECU",
      version: "v1.27",
      icon: Wrench,
      status: "installed",
      executable: "pcmtuner.exe",
      category: "Programming"
    },
    {
      id: 6,
      name: "DiamonD DTC Remover",
      description: "Alat penghapus DTC profesional",
      version: "v1.8.7",
      icon: FileText,
      status: "installed",
      executable: "diamond.exe",
      category: "Diagnostik"
    },
    {
      id: 7,
      name: "Swiftec RepairKit",
      description: "Perbaikan dan pemulihan ECU",
      version: "v3.1",
      icon: BarChart3,
      status: "installed",
      executable: "swiftec.exe",
      category: "Perbaikan"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'installed': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'updating': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'error': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'installed': return 'Terinstal';
      case 'updating': return 'Memperbarui';
      case 'error': return 'Error';
      default: return status;
    }
  };

  const handleLaunchApp = (executable: string, name: string) => {
    // Simulate launching Windows application
    if (window.electronAPI) {
      // If running in Electron
      window.electronAPI.launchApp(executable);
    } else {
      // Web fallback - show simulation
      alert(`Menjalankan ${name}...\nExecutable: ${executable}\n\nCatatan: Ini akan menjalankan aplikasi Windows yang sebenarnya di lingkungan desktop nyata.`);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigate('/')}
                className="hover:bg-accent"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali ke Dashboard
              </Button>
              <div className="w-10 h-10 rounded-lg bg-gradient-primary p-2">
                <img src={pasLogo} alt="PAS Logo" className="w-full h-full object-contain" />
              </div>
              <div>
                <h1 className="text-xl font-bold">Aplikasi Remap</h1>
                <p className="text-sm text-muted-foreground">Diluncurkan dari: {appName || 'Tidak Diketahui'}</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold mb-2">Aplikasi Windows Terinstal</h2>
          <p className="text-muted-foreground">Alat tuning dan diagnostik profesional siap diluncurkan</p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-green-500/20 flex items-center justify-center">
                  <Play className="w-5 h-5 text-green-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{installedApps.length}</p>
                  <p className="text-sm text-muted-foreground">Aplikasi Terinstal</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-blue-500/20 flex items-center justify-center">
                  <Monitor className="w-5 h-5 text-blue-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">5</p>
                  <p className="text-sm text-muted-foreground">Kategori</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-primary/20 flex items-center justify-center">
                  <Zap className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <p className="text-2xl font-bold">All</p>
                  <p className="text-sm text-muted-foreground">Siap Digunakan</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-yellow-500/20 flex items-center justify-center">
                  <Settings className="w-5 h-5 text-yellow-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">Updated</p>
                  <p className="text-sm text-muted-foreground">Versi Terbaru</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Applications Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {installedApps.map((app) => (
            <Card key={app.id} className="bg-gradient-card border-border hover:shadow-primary/20 transition-all duration-300 group">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="w-12 h-12 rounded-xl bg-gradient-primary p-3 group-hover:shadow-glow transition-all duration-300">
                    <app.icon className="w-full h-full text-primary-foreground" />
                  </div>
                  <Badge className={`${getStatusColor(app.status)} border`}>
                    {getStatusText(app.status)}
                  </Badge>
                </div>
                <div>
                  <CardTitle className="text-lg mb-1">{app.name}</CardTitle>
                  <CardDescription>{app.description}</CardDescription>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Versi:</span>
                    <span className="font-mono">{app.version}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Kategori:</span>
                    <Badge variant="secondary">{app.category}</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Executable:</span>
                    <span className="font-mono text-xs">{app.executable}</span>
                  </div>
                  <Button 
                    className="w-full bg-gradient-primary shadow-primary hover:shadow-glow transition-all duration-300"
                    onClick={() => handleLaunchApp(app.executable, app.name)}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Jalankan Aplikasi
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </main>
    </div>
  );
}