import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import AutoStartSettings from "@/components/AutoStartSettings";
import {
  ArrowLeft,
  Monitor,
  Settings,
  Zap,
  Gauge,
  FileText,
  BarChart3,
  Wrench,
  ExternalLink,
  Play,
  Terminal,
  FolderOpen,
  Copy,
  Download,
  Plus,
  X,
  Save
} from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import React, { useState, useEffect } from "react";
import pasLogo from "@/assets/pas-logo.png";

declare global {
  interface Window {
    electronAPI?: {
      launchApp: (executable: string, name: string, paths: string[]) => Promise<{
        success: boolean;
        message?: string;
        error?: string;
        path?: string;
      }>;
      startApp: (executable: string, name: string) => Promise<{
        success: boolean;
        message?: string;
        error?: string;
      }>;
      openExternal: (url: string) => Promise<void>;
      checkAppInstalled: (executable: string, paths: string[]) => Promise<{
        found: boolean;
        path?: string;
        version?: string;
        error?: string;
      }>;
      platform: string;
      isElectron: boolean;
    };
  }
}

export default function RemapPage() {
  const navigate = useNavigate();
  const { appName } = useParams<{ appName: string }>();
  const isElectron = window.electronAPI?.isElectron || false;
  const [installedApps, setInstalledApps] = useState<any[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [customApps, setCustomApps] = useState<any[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newApp, setNewApp] = useState({
    name: '',
    description: '',
    executable: '',
    category: 'Tuning',
    customPath: ''
  });

  // Database aplikasi remap yang tersedia (kosong - akan diisi manual)
  const availableApps: any[] = [];

  // Fungsi untuk scan aplikasi yang terinstall
  const scanInstalledApps = async () => {
    setIsScanning(true);
    const foundApps = [];
    const allApps = [...availableApps, ...customApps];

    if (isElectron && window.electronAPI) {
      // Mode Electron - scan real files
      for (const app of allApps) {
        try {
          const result = await window.electronAPI.checkAppInstalled?.(app.executable, app.paths);
          if (result?.found) {
            foundApps.push({
              ...app,
              status: "installed",
              version: result.version || "Terdeteksi",
              installedPath: result.path
            });
          } else {
            foundApps.push({
              ...app,
              status: "not_detected",
              version: "Tidak Terdeteksi",
              installedPath: null
            });
          }
        } catch (error) {
          console.log(`Could not check ${app.name}:`, error);
          foundApps.push({
            ...app,
            status: "error",
            version: "Error",
            installedPath: null
          });
        }
      }
    } else {
      // Mode Browser - tampilkan semua sebagai "tidak terdeteksi"
      allApps.forEach(app => {
        foundApps.push({
          ...app,
          status: "not_detected",
          version: "Tidak Terdeteksi",
          installedPath: null
        });
      });
    }

    setInstalledApps(foundApps);
    setIsScanning(false);
  };

  // Fungsi untuk menambahkan aplikasi custom
  const addCustomApp = () => {
    if (!newApp.name || !newApp.executable) {
      alert('Nama aplikasi dan file executable harus diisi!');
      return;
    }

    const customApp = {
      id: Date.now(),
      name: newApp.name,
      description: newApp.description || `Aplikasi ${newApp.name}`,
      icon: Zap, // Default icon
      executable: newApp.executable,
      category: newApp.category,
      paths: newApp.customPath ? [newApp.customPath] : [
        `C:\\Program Files\\${newApp.name}\\${newApp.executable}`,
        `C:\\Program Files (x86)\\${newApp.name}\\${newApp.executable}`,
        `C:\\${newApp.name}\\${newApp.executable}`,
        `D:\\${newApp.name}\\${newApp.executable}`
      ]
    };

    setCustomApps(prev => [...prev, customApp]);

    // Reset form
    setNewApp({
      name: '',
      description: '',
      executable: '',
      category: 'Tuning',
      customPath: ''
    });

    setShowAddForm(false);

    // Scan ulang setelah menambahkan
    setTimeout(() => {
      scanInstalledApps();
    }, 100);
  };

  const removeCustomApp = (id: number) => {
    setCustomApps(prev => prev.filter(app => app.id !== id));
    scanInstalledApps();
  };

  // Load custom apps dari localStorage saat komponen dimuat
  useEffect(() => {
    const savedApps = localStorage.getItem('customRemapApps');
    if (savedApps) {
      try {
        setCustomApps(JSON.parse(savedApps));
      } catch (error) {
        console.error('Error loading custom apps:', error);
      }
    }
  }, []);

  // Save custom apps ke localStorage setiap kali berubah
  useEffect(() => {
    if (customApps.length > 0) {
      localStorage.setItem('customRemapApps', JSON.stringify(customApps));
    }
  }, [customApps]);

  // Scan saat komponen dimuat
  useEffect(() => {
    scanInstalledApps();
  }, [isElectron, customApps]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'installed': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'not_detected': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'error': return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'scanning': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'installed': return 'Terinstal';
      case 'not_detected': return 'Tidak Terdeteksi';
      case 'error': return 'Error';
      case 'scanning': return 'Memindai...';
      default: return status;
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert(`✅ Disalin ke clipboard:\n${text}`);
    } catch (error) {
      alert(`📋 Silakan salin secara manual:\n${text}`);
    }
  };

  const openCommandPrompt = (executable: string) => {
    const command = `start "" "${executable}"`;
    copyToClipboard(command);
    alert(`💻 COMMAND PROMPT INSTRUCTION:

1. Tekan Windows + R
2. Ketik: cmd
3. Tekan Enter
4. Paste command berikut (Ctrl+V):
   ${command}
5. Tekan Enter

Command sudah disalin ke clipboard!`);
  };

  const downloadBatchFile = (executable: string, name: string) => {
    const appPaths: { [key: string]: string[] } = {
      'winols.exe': [
        'C:\\Program Files\\EVC\\WinOLS\\winols.exe',
        'C:\\Program Files (x86)\\EVC\\WinOLS\\winols.exe',
        'C:\\WinOLS\\winols.exe',
        'D:\\WinOLS\\winols.exe'
      ],
      'kessv2.exe': [
        'C:\\Program Files\\Alientech\\KESS V2\\kessv2.exe',
        'C:\\Program Files (x86)\\Alientech\\KESS V2\\kessv2.exe',
        'C:\\KESS V2\\kessv2.exe',
        'D:\\KESS V2\\kessv2.exe'
      ],
      'ktag.exe': [
        'C:\\Program Files\\Alientech\\KTAG\\ktag.exe',
        'C:\\Program Files (x86)\\Alientech\\KTAG\\ktag.exe',
        'C:\\KTAG\\ktag.exe',
        'D:\\KTAG\\ktag.exe'
      ],
      'autotuner.exe': [
        'C:\\Program Files\\AutoTuner\\autotuner.exe',
        'C:\\Program Files (x86)\\AutoTuner\\autotuner.exe',
        'C:\\AutoTuner\\autotuner.exe',
        'D:\\AutoTuner\\autotuner.exe'
      ],
      'pcmtuner.exe': [
        'C:\\Program Files\\PCMTuner\\pcmtuner.exe',
        'C:\\Program Files (x86)\\PCMTuner\\pcmtuner.exe',
        'C:\\PCMTuner\\pcmtuner.exe',
        'D:\\PCMTuner\\pcmtuner.exe'
      ],
      'diamond.exe': [
        'C:\\Program Files\\DiamonD\\diamond.exe',
        'C:\\Program Files (x86)\\DiamonD\\diamond.exe',
        'C:\\DiamonD\\diamond.exe',
        'D:\\DiamonD\\diamond.exe'
      ],
      'swiftec.exe': [
        'C:\\Program Files\\Swiftec\\swiftec.exe',
        'C:\\Program Files (x86)\\Swiftec\\swiftec.exe',
        'C:\\Swiftec\\swiftec.exe',
        'D:\\Swiftec\\swiftec.exe'
      ]
    };

    const paths = appPaths[executable] || [];

    let batchContent = `@echo off
echo ========================================
echo PAS Management System - ${name} Launcher
echo ========================================
echo.
echo Mencari ${name}...
echo.

`;

    paths.forEach((path, index) => {
      batchContent += `echo [${index + 1}] Mencoba: ${path}
if exist "${path}" (
    echo DITEMUKAN! Menjalankan ${name}...
    start "" "${path}"
    echo ${name} berhasil dijalankan!
    pause
    exit
)
echo File tidak ditemukan di lokasi ini.
echo.

`;
    });

    batchContent += `echo ========================================
echo APLIKASI TIDAK DITEMUKAN!
echo ========================================
echo.
echo ${name} tidak ditemukan di lokasi standar.
echo.
echo Silakan:
echo 1. Pastikan aplikasi sudah terinstal
echo 2. Cari file ${executable} secara manual
echo 3. Jalankan file tersebut dengan double-click
echo.
echo Lokasi yang sudah dicek:
`;

    paths.forEach((path, index) => {
      batchContent += `echo ${index + 1}. ${path}\n`;
    });

    batchContent += `echo.
pause`;

    // Create and download the batch file
    const blob = new Blob([batchContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `Launch_${name.replace(/\s+/g, '_')}.bat`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    alert(`📥 File batch "${a.download}" telah diunduh!

🚀 CARA MENGGUNAKAN:
1. Buka folder Downloads
2. Double-click file "${a.download}"
3. Script akan otomatis mencari dan menjalankan ${name}

💡 File batch ini akan mencoba semua lokasi instalasi umum secara otomatis.`);
  };

  const handleLaunchApp = async (executable: string, name: string) => {
    // Check if running in Electron
    if (window.electronAPI && window.electronAPI.isElectron) {
      try {
        // Find the app data to get paths
        const appData = installedApps.find(app => app.executable === executable);
        const paths = appData?.paths || [];

        console.log(`Launching ${name} with paths:`, paths);

        // Try to launch using Electron API
        const result = await window.electronAPI.launchApp(executable, name, paths);

        if (result.success) {
          alert(`✅ Berhasil menjalankan ${name}!

📍 Lokasi: ${result.path}
🚀 Aplikasi sedang dimuat...

${name} telah berhasil dijalankan dari aplikasi desktop PAS Auto Control Hub.`);
        } else {
          // Try fallback method
          const fallbackResult = await window.electronAPI.startApp(executable, name);

          if (fallbackResult.success) {
            alert(`✅ Berhasil menjalankan ${name} menggunakan Windows Start!

🚀 Aplikasi sedang dimuat...

${name} telah berhasil dijalankan menggunakan command Windows.`);
          } else {
            // Show manual instructions
            showManualInstructions(executable, name);
          }
        }
      } catch (error) {
        console.error('Error launching app:', error);
        showManualInstructions(executable, name);
      }
    } else {
      // Running in browser - show instructions
      showBrowserInstructions(executable, name);
    }
  };

  const showManualInstructions = (executable: string, name: string) => {
    const appPaths: { [key: string]: string[] } = {
      'winols.exe': [
        'C:\\Program Files\\EVC\\WinOLS\\winols.exe',
        'C:\\Program Files (x86)\\EVC\\WinOLS\\winols.exe',
        'C:\\WinOLS\\winols.exe',
        'D:\\WinOLS\\winols.exe'
      ],
      'kessv2.exe': [
        'C:\\Program Files\\Alientech\\KESS V2\\kessv2.exe',
        'C:\\Program Files (x86)\\Alientech\\KESS V2\\kessv2.exe',
        'C:\\KESS V2\\kessv2.exe',
        'D:\\KESS V2\\kessv2.exe'
      ],
      'ktag.exe': [
        'C:\\Program Files\\Alientech\\KTAG\\ktag.exe',
        'C:\\Program Files (x86)\\Alientech\\KTAG\\ktag.exe',
        'C:\\KTAG\\ktag.exe',
        'D:\\KTAG\\ktag.exe'
      ],
      'autotuner.exe': [
        'C:\\Program Files\\AutoTuner\\autotuner.exe',
        'C:\\Program Files (x86)\\AutoTuner\\autotuner.exe',
        'C:\\AutoTuner\\autotuner.exe',
        'D:\\AutoTuner\\autotuner.exe'
      ],
      'pcmtuner.exe': [
        'C:\\Program Files\\PCMTuner\\pcmtuner.exe',
        'C:\\Program Files (x86)\\PCMTuner\\pcmtuner.exe',
        'C:\\PCMTuner\\pcmtuner.exe',
        'D:\\PCMTuner\\pcmtuner.exe'
      ],
      'diamond.exe': [
        'C:\\Program Files\\DiamonD\\diamond.exe',
        'C:\\Program Files (x86)\\DiamonD\\diamond.exe',
        'C:\\DiamonD\\diamond.exe',
        'D:\\DiamonD\\diamond.exe'
      ],
      'swiftec.exe': [
        'C:\\Program Files\\Swiftec\\swiftec.exe',
        'C:\\Program Files (x86)\\Swiftec\\swiftec.exe',
        'C:\\Swiftec\\swiftec.exe',
        'D:\\Swiftec\\swiftec.exe'
      ]
    };

    const paths = appPaths[executable] || [];
    const pathsList = paths.map(path => `• ${path}`).join('\n');

    alert(`⚠️ ${name} tidak ditemukan di lokasi standar

📁 LOKASI YANG DICEK:
${pathsList}

📋 CARA MANUAL:
1. Buka File Explorer (Windows + E)
2. Cari file "${executable}" di:
   - Desktop
   - Folder Downloads
   - Program Files
3. Double-click untuk menjalankan

💡 TIPS:
• Gunakan Windows Search (Windows + S)
• Ketik "${executable.replace('.exe', '')}" untuk mencari
• Pastikan aplikasi sudah terinstal dengan benar`);
  };

  const showBrowserInstructions = (executable: string, name: string) => {
    const appPaths: { [key: string]: string[] } = {
      'winols.exe': [
        'C:\\Program Files\\EVC\\WinOLS\\winols.exe',
        'C:\\Program Files (x86)\\EVC\\WinOLS\\winols.exe',
        'C:\\WinOLS\\winols.exe',
        'D:\\WinOLS\\winols.exe'
      ],
      'kessv2.exe': [
        'C:\\Program Files\\Alientech\\KESS V2\\kessv2.exe',
        'C:\\Program Files (x86)\\Alientech\\KESS V2\\kessv2.exe',
        'C:\\KESS V2\\kessv2.exe',
        'D:\\KESS V2\\kessv2.exe'
      ],
      'ktag.exe': [
        'C:\\Program Files\\Alientech\\KTAG\\ktag.exe',
        'C:\\Program Files (x86)\\Alientech\\KTAG\\ktag.exe',
        'C:\\KTAG\\ktag.exe',
        'D:\\KTAG\\ktag.exe'
      ],
      'autotuner.exe': [
        'C:\\Program Files\\AutoTuner\\autotuner.exe',
        'C:\\Program Files (x86)\\AutoTuner\\autotuner.exe',
        'C:\\AutoTuner\\autotuner.exe',
        'D:\\AutoTuner\\autotuner.exe'
      ],
      'pcmtuner.exe': [
        'C:\\Program Files\\PCMTuner\\pcmtuner.exe',
        'C:\\Program Files (x86)\\PCMTuner\\pcmtuner.exe',
        'C:\\PCMTuner\\pcmtuner.exe',
        'D:\\PCMTuner\\pcmtuner.exe'
      ],
      'diamond.exe': [
        'C:\\Program Files\\DiamonD\\diamond.exe',
        'C:\\Program Files (x86)\\DiamonD\\diamond.exe',
        'C:\\DiamonD\\diamond.exe',
        'D:\\DiamonD\\diamond.exe'
      ],
      'swiftec.exe': [
        'C:\\Program Files\\Swiftec\\swiftec.exe',
        'C:\\Program Files (x86)\\Swiftec\\swiftec.exe',
        'C:\\Swiftec\\swiftec.exe',
        'D:\\Swiftec\\swiftec.exe'
      ]
    };

    const paths = appPaths[executable] || [];
    const pathsList = paths.map(path => `• ${path}`).join('\n');

    const message = `🌐 Mode Browser - ${name}

📁 LOKASI INSTALASI UMUM:
${pathsList}

📋 CARA MENJALANKAN:
1. Buka File Explorer (Windows + E)
2. Navigasi ke salah satu lokasi di atas
3. Cari file "${executable}"
4. Double-click untuk menjalankan

💡 TIPS:
• Gunakan Windows Search (Windows + S) untuk mencari "${executable.replace('.exe', '')}"
• Gunakan tombol CMD atau .BAT untuk bantuan otomatis

🚀 UNTUK FITUR OTOMATIS:
Jalankan aplikasi sebagai desktop app dengan:
npm run electron:dev

⚠️ CATATAN:
Browser tidak dapat langsung menjalankan aplikasi Windows karena keamanan.
Gunakan versi desktop untuk fitur launch otomatis.`;

    alert(message);

    // Try to open file explorer to common installation directory
    try {
      if (window.electronAPI) {
        window.electronAPI.openExternal('file:///C:/Program Files/');
      } else {
        window.open('file:///C:/Program Files/', '_blank');
      }
    } catch (error) {
      console.log('Cannot open file explorer');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigate('/')}
                className="hover:bg-accent"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali ke Dashboard
              </Button>
              <div className="w-10 h-10 rounded-lg bg-gradient-primary p-2">
                <img src={pasLogo} alt="PAS Logo" className="w-full h-full object-contain" />
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <h1 className="text-xl font-bold">Aplikasi Remap</h1>
                  <Badge className={isElectron ? 'bg-green-500/20 text-green-400 border-green-500/30' : 'bg-blue-500/20 text-blue-400 border-blue-500/30'}>
                    {isElectron ? '🖥️ Desktop' : '🌐 Browser'}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  Diluncurkan dari: {appName || 'Tidak Diketahui'}
                  {isElectron && ' • Mode Desktop Aktif'}
                  {installedApps.length > 0 && ` • ${installedApps.filter(app => app.status === 'installed').length}/${installedApps.length} Terinstal`}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAddForm(true)}
                className="hover:bg-accent"
              >
                <Plus className="w-4 h-4 mr-2" />
                Tambah Aplikasi
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={scanInstalledApps}
                disabled={isScanning}
                className="hover:bg-accent"
              >
                {isScanning ? (
                  <>
                    <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    Memindai...
                  </>
                ) : (
                  <>
                    <FolderOpen className="w-4 h-4 mr-2" />
                    Pindai Ulang
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Add App Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md bg-gradient-card border-border">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Tambah Aplikasi Remap</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAddForm(false)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
              <CardDescription>
                Tambahkan aplikasi remap yang terinstall di sistem Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="appName">Nama Aplikasi *</Label>
                <Input
                  id="appName"
                  placeholder="Contoh: WinOLS Professional"
                  value={newApp.name}
                  onChange={(e) => setNewApp(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="appDescription">Deskripsi</Label>
                <Textarea
                  id="appDescription"
                  placeholder="Deskripsi singkat aplikasi"
                  value={newApp.description}
                  onChange={(e) => setNewApp(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="appExecutable">File Executable *</Label>
                <Input
                  id="appExecutable"
                  placeholder="Contoh: winols.exe"
                  value={newApp.executable}
                  onChange={(e) => setNewApp(prev => ({ ...prev, executable: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="appCategory">Kategori</Label>
                <Select value={newApp.category} onValueChange={(value) => setNewApp(prev => ({ ...prev, category: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Tuning">Tuning</SelectItem>
                    <SelectItem value="Tuning ECU">Tuning ECU</SelectItem>
                    <SelectItem value="Pemrograman">Pemrograman</SelectItem>
                    <SelectItem value="Diagnostik">Diagnostik</SelectItem>
                    <SelectItem value="Perbaikan">Perbaikan</SelectItem>
                    <SelectItem value="Custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="appPath">Path Khusus (Opsional)</Label>
                <Input
                  id="appPath"
                  placeholder="C:\Program Files\AppName\app.exe"
                  value={newApp.customPath}
                  onChange={(e) => setNewApp(prev => ({ ...prev, customPath: e.target.value }))}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Kosongkan untuk menggunakan path standar
                </p>
              </div>

              <div className="flex space-x-2 pt-4">
                <Button onClick={addCustomApp} className="flex-1">
                  <Save className="w-4 h-4 mr-2" />
                  Simpan
                </Button>
                <Button variant="outline" onClick={() => setShowAddForm(false)} className="flex-1">
                  Batal
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <main className="container mx-auto px-6 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold mb-2">Aplikasi Windows Terinstal</h2>
          <p className="text-muted-foreground mb-4">Alat tuning dan diagnostik profesional siap diluncurkan</p>

          {/* Instructions Card */}
          <Card className="bg-gradient-card border-border border-yellow-500/30">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 rounded-lg bg-yellow-500/20 flex items-center justify-center flex-shrink-0">
                  <FolderOpen className="w-4 h-4 text-yellow-400" />
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold text-yellow-400">
                    Cara Menjalankan Aplikasi {isElectron ? '(Mode Desktop)' : '(Mode Browser)'}
                  </h3>
                  <div className="text-sm text-muted-foreground space-y-1">
                    {isElectron ? (
                      <>
                        <p>• <strong>Panduan Menjalankan:</strong> Klik untuk menjalankan aplikasi secara otomatis</p>
                        <p>• <strong>CMD:</strong> Salin command untuk menjalankan via Command Prompt</p>
                        <p>• <strong>Salin:</strong> Salin nama file executable untuk pencarian manual</p>
                        <p>• <strong>.BAT:</strong> Unduh script otomatis untuk menjalankan aplikasi</p>
                        <p className="text-green-400">✅ <strong>Mode Desktop:</strong> Aplikasi dapat dijalankan langsung!</p>
                      </>
                    ) : (
                      <>
                        <p>• <strong>Panduan Menjalankan:</strong> Klik untuk melihat lokasi instalasi dan instruksi lengkap</p>
                        <p>• <strong>CMD:</strong> Salin command untuk menjalankan via Command Prompt</p>
                        <p>• <strong>Salin:</strong> Salin nama file executable untuk pencarian manual</p>
                        <p>• <strong>.BAT:</strong> Unduh script otomatis untuk menjalankan aplikasi</p>
                        <p className="text-blue-400">ℹ️ <strong>Mode Browser:</strong> Gunakan bantuan manual atau jalankan: npm run electron:dev</p>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-green-500/20 flex items-center justify-center">
                  <Play className="w-5 h-5 text-green-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{installedApps.filter(app => app.status === 'installed').length}</p>
                  <p className="text-sm text-muted-foreground">Aplikasi Terinstal</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-blue-500/20 flex items-center justify-center">
                  <Monitor className="w-5 h-5 text-blue-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{[...new Set(installedApps.map(app => app.category))].length}</p>
                  <p className="text-sm text-muted-foreground">Kategori</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-primary/20 flex items-center justify-center">
                  <Zap className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{installedApps.filter(app => app.status === 'installed').length}</p>
                  <p className="text-sm text-muted-foreground">Siap Digunakan</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-yellow-500/20 flex items-center justify-center">
                  <Settings className="w-5 h-5 text-yellow-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{installedApps.length}</p>
                  <p className="text-sm text-muted-foreground">Total Tersedia</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Auto-Start Settings */}
        <AutoStartSettings />

        {/* Applications Grid */}
        {installedApps.length === 0 ? (
          <Card className="bg-gradient-card border-border border-dashed">
            <CardContent className="p-12 text-center">
              <div className="w-16 h-16 rounded-full bg-muted/20 flex items-center justify-center mx-auto mb-4">
                <Plus className="w-8 h-8 text-muted-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Belum Ada Aplikasi</h3>
              <p className="text-muted-foreground mb-6">
                Tambahkan aplikasi remap yang terinstall di sistem Anda untuk mulai menggunakan launcher ini.
              </p>
              <Button onClick={() => setShowAddForm(true)} className="bg-gradient-primary">
                <Plus className="w-4 h-4 mr-2" />
                Tambah Aplikasi Pertama
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {installedApps.map((app) => (
            <Card key={app.id} className="bg-gradient-card border-border hover:shadow-primary/20 transition-all duration-300 group">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="w-12 h-12 rounded-xl bg-gradient-primary p-3 group-hover:shadow-glow transition-all duration-300">
                    <Zap className="w-full h-full text-primary-foreground" />
                  </div>
                  <Badge className={`${getStatusColor(app.status)} border`}>
                    {getStatusText(app.status)}
                  </Badge>
                </div>
                <div>
                  <CardTitle className="text-lg mb-1">{app.name}</CardTitle>
                  <CardDescription>{app.description}</CardDescription>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Versi:</span>
                    <span className="font-mono">{app.version}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Kategori:</span>
                    <Badge variant="secondary">{app.category}</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Executable:</span>
                    <span className="font-mono text-xs">{app.executable}</span>
                  </div>
                  {app.installedPath && (
                    <div className="text-sm">
                      <span className="text-muted-foreground">Lokasi:</span>
                      <p className="font-mono text-xs text-green-400 mt-1 break-all">{app.installedPath}</p>
                    </div>
                  )}
                  <div className="space-y-2">
                    <Button
                      className={`w-full shadow-primary hover:shadow-glow transition-all duration-300 ${
                        app.status === 'installed'
                          ? 'bg-gradient-primary'
                          : 'bg-gray-600 hover:bg-gray-500'
                      }`}
                      onClick={() => handleLaunchApp(app.executable, app.name)}
                      disabled={app.status === 'not_detected' && !isElectron}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      {app.status === 'installed'
                        ? (isElectron ? 'Jalankan Aplikasi' : 'Panduan Menjalankan')
                        : 'Tidak Terdeteksi'
                      }
                    </Button>

                    <div className="grid grid-cols-3 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openCommandPrompt(app.executable)}
                        className="text-xs"
                      >
                        <Terminal className="w-3 h-3 mr-1" />
                        CMD
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(app.executable)}
                        className="text-xs"
                      >
                        <Copy className="w-3 h-3 mr-1" />
                        Salin
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadBatchFile(app.executable, app.name)}
                        className="text-xs"
                      >
                        <Download className="w-3 h-3 mr-1" />
                        .BAT
                      </Button>
                    </div>

                    {/* Tombol hapus untuk aplikasi custom */}
                    {customApps.some(customApp => customApp.id === app.id) && (
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => {
                          if (confirm(`Hapus aplikasi ${app.name}?`)) {
                            removeCustomApp(app.id);
                          }
                        }}
                        className="w-full mt-2 text-xs"
                      >
                        <X className="w-3 h-3 mr-1" />
                        Hapus Aplikasi
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
            ))}
          </div>
        )}
      </main>
    </div>
  );
}