import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Monitor,
  Settings,
  Zap,
  Gauge,
  FileText,
  BarChart3,
  Wrench,
  ExternalLink,
  Play,
  Terminal,
  FolderOpen,
  Copy,
  Download
} from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import pasLogo from "@/assets/pas-logo.png";

declare global {
  interface Window {
    electronAPI?: {
      launchApp: (executable: string) => void;
    };
  }
}

export default function RemapPage() {
  const navigate = useNavigate();
  const { appName } = useParams<{ appName: string }>();

  const installedApps = [
    {
      id: 1,
      name: "WinOLS Professional",
      description: "Software tuning ECU profesional",
      version: "v5.26",
      icon: Zap,
      status: "installed",
      executable: "winols.exe",
      category: "Tuning ECU"
    },
    {
      id: 2,
      name: "KESS V2",
      description: "Alat pemrograman ECU master",
      version: "v5.017",
      icon: Monitor,
      status: "installed", 
      executable: "kessv2.exe",
      category: "Pemrograman"
    },
    {
      id: 3,
      name: "KTAG Master",
      description: "Pemrograman ECU via JTAG/BDM",
      version: "v7.020",
      icon: Settings,
      status: "installed",
      executable: "ktag.exe", 
      category: "Programming"
    },
    {
      id: 4,
      name: "AutoTuner Tool",
      description: "Solusi tuning profesional",
      version: "v4.2",
      icon: Gauge,
      status: "installed",
      executable: "autotuner.exe",
      category: "Tuning"
    },
    {
      id: 5,
      name: "PCMTuner",
      description: "Pembacaan dan penulisan PCM/ECU",
      version: "v1.27",
      icon: Wrench,
      status: "installed",
      executable: "pcmtuner.exe",
      category: "Programming"
    },
    {
      id: 6,
      name: "DiamonD DTC Remover",
      description: "Alat penghapus DTC profesional",
      version: "v1.8.7",
      icon: FileText,
      status: "installed",
      executable: "diamond.exe",
      category: "Diagnostik"
    },
    {
      id: 7,
      name: "Swiftec RepairKit",
      description: "Perbaikan dan pemulihan ECU",
      version: "v3.1",
      icon: BarChart3,
      status: "installed",
      executable: "swiftec.exe",
      category: "Perbaikan"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'installed': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'updating': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'error': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'installed': return 'Terinstal';
      case 'updating': return 'Memperbarui';
      case 'error': return 'Error';
      default: return status;
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert(`✅ Disalin ke clipboard:\n${text}`);
    } catch (error) {
      alert(`📋 Silakan salin secara manual:\n${text}`);
    }
  };

  const openCommandPrompt = (executable: string) => {
    const command = `start "" "${executable}"`;
    copyToClipboard(command);
    alert(`💻 COMMAND PROMPT INSTRUCTION:

1. Tekan Windows + R
2. Ketik: cmd
3. Tekan Enter
4. Paste command berikut (Ctrl+V):
   ${command}
5. Tekan Enter

Command sudah disalin ke clipboard!`);
  };

  const downloadBatchFile = (executable: string, name: string) => {
    const appPaths: { [key: string]: string[] } = {
      'winols.exe': [
        'C:\\Program Files\\EVC\\WinOLS\\winols.exe',
        'C:\\Program Files (x86)\\EVC\\WinOLS\\winols.exe',
        'C:\\WinOLS\\winols.exe',
        'D:\\WinOLS\\winols.exe'
      ],
      'kessv2.exe': [
        'C:\\Program Files\\Alientech\\KESS V2\\kessv2.exe',
        'C:\\Program Files (x86)\\Alientech\\KESS V2\\kessv2.exe',
        'C:\\KESS V2\\kessv2.exe',
        'D:\\KESS V2\\kessv2.exe'
      ],
      'ktag.exe': [
        'C:\\Program Files\\Alientech\\KTAG\\ktag.exe',
        'C:\\Program Files (x86)\\Alientech\\KTAG\\ktag.exe',
        'C:\\KTAG\\ktag.exe',
        'D:\\KTAG\\ktag.exe'
      ],
      'autotuner.exe': [
        'C:\\Program Files\\AutoTuner\\autotuner.exe',
        'C:\\Program Files (x86)\\AutoTuner\\autotuner.exe',
        'C:\\AutoTuner\\autotuner.exe',
        'D:\\AutoTuner\\autotuner.exe'
      ],
      'pcmtuner.exe': [
        'C:\\Program Files\\PCMTuner\\pcmtuner.exe',
        'C:\\Program Files (x86)\\PCMTuner\\pcmtuner.exe',
        'C:\\PCMTuner\\pcmtuner.exe',
        'D:\\PCMTuner\\pcmtuner.exe'
      ],
      'diamond.exe': [
        'C:\\Program Files\\DiamonD\\diamond.exe',
        'C:\\Program Files (x86)\\DiamonD\\diamond.exe',
        'C:\\DiamonD\\diamond.exe',
        'D:\\DiamonD\\diamond.exe'
      ],
      'swiftec.exe': [
        'C:\\Program Files\\Swiftec\\swiftec.exe',
        'C:\\Program Files (x86)\\Swiftec\\swiftec.exe',
        'C:\\Swiftec\\swiftec.exe',
        'D:\\Swiftec\\swiftec.exe'
      ]
    };

    const paths = appPaths[executable] || [];

    let batchContent = `@echo off
echo ========================================
echo PAS Management System - ${name} Launcher
echo ========================================
echo.
echo Mencari ${name}...
echo.

`;

    paths.forEach((path, index) => {
      batchContent += `echo [${index + 1}] Mencoba: ${path}
if exist "${path}" (
    echo DITEMUKAN! Menjalankan ${name}...
    start "" "${path}"
    echo ${name} berhasil dijalankan!
    pause
    exit
)
echo File tidak ditemukan di lokasi ini.
echo.

`;
    });

    batchContent += `echo ========================================
echo APLIKASI TIDAK DITEMUKAN!
echo ========================================
echo.
echo ${name} tidak ditemukan di lokasi standar.
echo.
echo Silakan:
echo 1. Pastikan aplikasi sudah terinstal
echo 2. Cari file ${executable} secara manual
echo 3. Jalankan file tersebut dengan double-click
echo.
echo Lokasi yang sudah dicek:
`;

    paths.forEach((path, index) => {
      batchContent += `echo ${index + 1}. ${path}\n`;
    });

    batchContent += `echo.
pause`;

    // Create and download the batch file
    const blob = new Blob([batchContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `Launch_${name.replace(/\s+/g, '_')}.bat`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    alert(`📥 File batch "${a.download}" telah diunduh!

🚀 CARA MENGGUNAKAN:
1. Buka folder Downloads
2. Double-click file "${a.download}"
3. Script akan otomatis mencari dan menjalankan ${name}

💡 File batch ini akan mencoba semua lokasi instalasi umum secara otomatis.`);
  };

  const handleLaunchApp = async (executable: string, name: string) => {
    // Common installation paths for each application
    const appPaths: { [key: string]: string[] } = {
      'winols.exe': [
        'C:\\Program Files\\EVC\\WinOLS\\winols.exe',
        'C:\\Program Files (x86)\\EVC\\WinOLS\\winols.exe',
        'C:\\WinOLS\\winols.exe',
        'D:\\WinOLS\\winols.exe'
      ],
      'kessv2.exe': [
        'C:\\Program Files\\Alientech\\KESS V2\\kessv2.exe',
        'C:\\Program Files (x86)\\Alientech\\KESS V2\\kessv2.exe',
        'C:\\KESS V2\\kessv2.exe',
        'D:\\KESS V2\\kessv2.exe'
      ],
      'ktag.exe': [
        'C:\\Program Files\\Alientech\\KTAG\\ktag.exe',
        'C:\\Program Files (x86)\\Alientech\\KTAG\\ktag.exe',
        'C:\\KTAG\\ktag.exe',
        'D:\\KTAG\\ktag.exe'
      ],
      'autotuner.exe': [
        'C:\\Program Files\\AutoTuner\\autotuner.exe',
        'C:\\Program Files (x86)\\AutoTuner\\autotuner.exe',
        'C:\\AutoTuner\\autotuner.exe',
        'D:\\AutoTuner\\autotuner.exe'
      ],
      'pcmtuner.exe': [
        'C:\\Program Files\\PCMTuner\\pcmtuner.exe',
        'C:\\Program Files (x86)\\PCMTuner\\pcmtuner.exe',
        'C:\\PCMTuner\\pcmtuner.exe',
        'D:\\PCMTuner\\pcmtuner.exe'
      ],
      'diamond.exe': [
        'C:\\Program Files\\DiamonD\\diamond.exe',
        'C:\\Program Files (x86)\\DiamonD\\diamond.exe',
        'C:\\DiamonD\\diamond.exe',
        'D:\\DiamonD\\diamond.exe'
      ],
      'swiftec.exe': [
        'C:\\Program Files\\Swiftec\\swiftec.exe',
        'C:\\Program Files (x86)\\Swiftec\\swiftec.exe',
        'C:\\Swiftec\\swiftec.exe',
        'D:\\Swiftec\\swiftec.exe'
      ]
    };

    const paths = appPaths[executable] || [];

    // Create a comprehensive instruction message
    const pathsList = paths.map(path => `• ${path}`).join('\n');

    const message = `🚀 Menjalankan ${name}

📁 LOKASI INSTALASI UMUM:
${pathsList}

📋 CARA MENJALANKAN:
1. Buka File Explorer (Windows + E)
2. Navigasi ke salah satu lokasi di atas
3. Cari file "${executable}"
4. Double-click untuk menjalankan

💡 TIPS:
• Jika aplikasi tidak ada di lokasi tersebut, cari di:
  - Desktop
  - Folder Downloads
  - C:\\Users\\<USER>\\Desktop

• Gunakan Windows Search (Windows + S) untuk mencari "${executable.replace('.exe', '')}"

⚠️ CATATAN:
Aplikasi web tidak dapat langsung menjalankan program Windows karena keamanan browser.
Fitur ini akan bekerja penuh jika aplikasi dijalankan sebagai aplikasi desktop (Electron).`;

    alert(message);

    // Try to open file explorer to common installation directory
    try {
      // This will only work in some browsers and may be blocked
      window.open('file:///C:/Program Files/', '_blank');
    } catch (error) {
      console.log('Cannot open file explorer from browser');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigate('/')}
                className="hover:bg-accent"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali ke Dashboard
              </Button>
              <div className="w-10 h-10 rounded-lg bg-gradient-primary p-2">
                <img src={pasLogo} alt="PAS Logo" className="w-full h-full object-contain" />
              </div>
              <div>
                <h1 className="text-xl font-bold">Aplikasi Remap</h1>
                <p className="text-sm text-muted-foreground">Diluncurkan dari: {appName || 'Tidak Diketahui'}</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold mb-2">Aplikasi Windows Terinstal</h2>
          <p className="text-muted-foreground mb-4">Alat tuning dan diagnostik profesional siap diluncurkan</p>

          {/* Instructions Card */}
          <Card className="bg-gradient-card border-border border-yellow-500/30">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 rounded-lg bg-yellow-500/20 flex items-center justify-center flex-shrink-0">
                  <FolderOpen className="w-4 h-4 text-yellow-400" />
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold text-yellow-400">Cara Menjalankan Aplikasi</h3>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p>• <strong>Panduan Menjalankan:</strong> Klik untuk melihat lokasi instalasi dan instruksi lengkap</p>
                    <p>• <strong>CMD:</strong> Salin command untuk menjalankan via Command Prompt</p>
                    <p>• <strong>Salin:</strong> Salin nama file executable untuk pencarian manual</p>
                    <p>• <strong>.BAT:</strong> Unduh script otomatis untuk menjalankan aplikasi</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-green-500/20 flex items-center justify-center">
                  <Play className="w-5 h-5 text-green-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{installedApps.length}</p>
                  <p className="text-sm text-muted-foreground">Aplikasi Terinstal</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-blue-500/20 flex items-center justify-center">
                  <Monitor className="w-5 h-5 text-blue-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">5</p>
                  <p className="text-sm text-muted-foreground">Kategori</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-primary/20 flex items-center justify-center">
                  <Zap className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <p className="text-2xl font-bold">All</p>
                  <p className="text-sm text-muted-foreground">Siap Digunakan</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card border-border">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-lg bg-yellow-500/20 flex items-center justify-center">
                  <Settings className="w-5 h-5 text-yellow-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">Updated</p>
                  <p className="text-sm text-muted-foreground">Versi Terbaru</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Applications Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {installedApps.map((app) => (
            <Card key={app.id} className="bg-gradient-card border-border hover:shadow-primary/20 transition-all duration-300 group">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="w-12 h-12 rounded-xl bg-gradient-primary p-3 group-hover:shadow-glow transition-all duration-300">
                    <app.icon className="w-full h-full text-primary-foreground" />
                  </div>
                  <Badge className={`${getStatusColor(app.status)} border`}>
                    {getStatusText(app.status)}
                  </Badge>
                </div>
                <div>
                  <CardTitle className="text-lg mb-1">{app.name}</CardTitle>
                  <CardDescription>{app.description}</CardDescription>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Versi:</span>
                    <span className="font-mono">{app.version}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Kategori:</span>
                    <Badge variant="secondary">{app.category}</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Executable:</span>
                    <span className="font-mono text-xs">{app.executable}</span>
                  </div>
                  <div className="space-y-2">
                    <Button
                      className="w-full bg-gradient-primary shadow-primary hover:shadow-glow transition-all duration-300"
                      onClick={() => handleLaunchApp(app.executable, app.name)}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Panduan Menjalankan
                    </Button>

                    <div className="grid grid-cols-3 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openCommandPrompt(app.executable)}
                        className="text-xs"
                      >
                        <Terminal className="w-3 h-3 mr-1" />
                        CMD
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(app.executable)}
                        className="text-xs"
                      >
                        <Copy className="w-3 h-3 mr-1" />
                        Salin
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadBatchFile(app.executable, app.name)}
                        className="text-xs"
                      >
                        <Download className="w-3 h-3 mr-1" />
                        .BAT
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </main>
    </div>
  );
}