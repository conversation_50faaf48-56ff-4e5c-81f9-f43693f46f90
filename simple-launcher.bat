@echo off
echo ========================================
echo PAS Auto Control Hub - Simple Launcher
echo ========================================
echo.

:: Set working directory to script location
cd /d "%~dp0"

echo Current directory: %cd%
echo.

echo STEP 1: Starting Development Server...
echo ========================================
echo.
echo Opening new window for development server...
start "Vite Dev Server" cmd /k "echo Starting Vite Development Server... && npx vite --port 8080"

echo.
echo STEP 2: Waiting for Development Server...
echo ========================================
echo.
echo Please wait 10-15 seconds for the development server to start...
echo You should see "Local: http://localhost:8080" in the new window.
echo.

:: Wait for user confirmation
echo Press any key when you see the development server is ready...
pause

echo.
echo STEP 3: Starting Electron Application...
echo ========================================
echo.
set NODE_ENV=development
echo Starting PAS Auto Control Hub...
start "PAS Auto Control Hub" npx electron .

echo.
echo ========================================
echo LAUNCH COMPLETED!
echo ========================================
echo.
echo What should happen now:
echo 1. Development server window is open (keep it open)
echo 2. PAS Auto Control Hub window should appear
echo 3. You should see the login screen
echo.
echo If login screen doesn't appear:
echo - Check taskbar for "PAS Auto Control Hub"
echo - Look for Electron window
echo - Wait a few more seconds
echo.
echo To stop everything:
echo 1. Close PAS Auto Control Hub window
echo 2. Close development server window
echo 3. Close this window
echo.

echo Press any key to close this launcher...
pause
exit
