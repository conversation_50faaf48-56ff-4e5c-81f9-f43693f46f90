const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  launchApp: (executable, name) => 
    ipcRenderer.invoke('launch-app', { executable, name }),
  
  startApp: (executable, name) => 
    ipcRenderer.invoke('start-app', { executable, name }),
  
  openExternal: (url) => 
    ipcRenderer.invoke('open-external', url),
  
  // Platform info
  platform: process.platform,
  isElectron: true
});
