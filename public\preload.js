const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  launchApp: (executable, name, paths) =>
    ipcRenderer.invoke('launch-app', { executable, name, paths }),

  startApp: (executable, name) =>
    ipcRenderer.invoke('start-app', { executable, name }),

  openExternal: (url) =>
    ipcRenderer.invoke('open-external', url),

  checkAppInstalled: (executable, paths) =>
    ipcRenderer.invoke('check-app-installed', { executable, paths }),

  // Platform info
  platform: process.platform,
  isElectron: true
});
