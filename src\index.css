@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* PAS Automotive Theme - Professional Workshop Colors */
    --background: 220 27% 8%;
    --foreground: 210 40% 98%;

    --card: 220 23% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 220 23% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 14 100% 57%;
    --primary-foreground: 220 27% 8%;

    --secondary: 220 20% 15%;
    --secondary-foreground: 210 40% 98%;

    --muted: 220 20% 15%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 100% 60%;
    --accent-foreground: 220 27% 8%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 20% 18%;
    --input: 220 20% 15%;
    --ring: 14 100% 57%;

    /* Premium automotive gradients */
    --gradient-primary: linear-gradient(135deg, hsl(14 100% 57%), hsl(25 100% 45%));
    --gradient-secondary: linear-gradient(135deg, hsl(217 100% 60%), hsl(237 100% 68%));
    --gradient-dark: linear-gradient(135deg, hsl(220 27% 8%), hsl(220 23% 11%));
    --gradient-card: linear-gradient(135deg, hsl(220 23% 11%), hsl(220 20% 15%));
    
    /* Premium shadows */
    --shadow-primary: 0 10px 30px -10px hsl(14 100% 57% / 0.3);
    --shadow-secondary: 0 10px 30px -10px hsl(217 100% 60% / 0.3);
    --shadow-glow: 0 0 40px hsl(14 100% 57% / 0.2);
    --shadow-card: 0 8px 25px -5px hsl(220 27% 8% / 0.4);
    
    /* Animation tokens */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}