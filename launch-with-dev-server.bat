@echo off
echo ========================================
echo PAS Auto Control Hub - Complete Launcher
echo ========================================

:: Set working directory to script location
cd /d "%~dp0"

:: Kill any existing processes
echo [INFO] Cleaning up existing processes...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im electron.exe >nul 2>&1
timeout /t 2 /nobreak > nul

echo [INFO] Starting PAS Auto Control Hub...
echo.

:: Start Vite development server in background
echo [1/2] Starting development server...
start "Vite Dev Server" /min cmd /c "npx vite --host 0.0.0.0 --port 8080"

:: Wait for dev server to start
echo [INFO] Waiting for development server to start...
timeout /t 8 /nobreak > nul

:: Check if dev server is running
echo [INFO] Checking development server...
curl -s http://localhost:8080 >nul 2>&1
if %errorlevel%==0 (
    echo [SUCCESS] Development server is running!
) else (
    echo [WARNING] Development server might still be starting...
)

:: Start Electron in development mode
echo [2/2] Starting Electron application...
set NODE_ENV=development
start "PAS Auto Control Hub" npx electron .

echo.
echo ========================================
echo PAS Auto Control Hub Started!
echo ========================================
echo.
echo Status:
echo - Development Server: http://localhost:8080
echo - Electron App: Starting...
echo.
echo If login window doesn't appear:
echo 1. Wait 10-15 seconds for full startup
echo 2. Check taskbar for PAS Auto Control Hub
echo 3. Look for Electron window
echo 4. Check system tray for PAS icon
echo.
echo To stop everything:
echo 1. Close Electron window
echo 2. Close this command window
echo.

:: Keep this window open to monitor
echo Press Ctrl+C to stop all processes, or close this window.
echo.
pause
