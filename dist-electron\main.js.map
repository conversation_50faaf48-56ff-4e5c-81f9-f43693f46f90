{"version": 3, "file": "main.js", "sourceRoot": "", "sources": ["../electron/main.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAuF;AACvF,iDAA4C;AAC5C,2CAA6B;AAC7B,uCAAyB;AACzB,8DAAqC;AAErC,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;AAErD,4BAA4B;AAC5B,MAAM,YAAY,GAAG,IAAI,qBAAU,CAAC;IAClC,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE,cAAG,CAAC,OAAO,CAAC,KAAK,CAAC;IACxB,QAAQ,EAAE,KAAK,EAAE,gDAAgD;CAClE,CAAC,CAAC;AAEH,mBAAmB;AACnB,IAAI,UAAU,GAAyB,IAAI,CAAC;AAC5C,IAAI,IAAI,GAAgB,IAAI,CAAC;AAC7B,IAAI,SAAS,GAAG,KAAK,CAAC;AAEtB,uFAAuF;AACvF,MAAM,WAAW,GAAgC,EAAE,CAAC;AAEpD,SAAS,YAAY;IACnB,4BAA4B;IAC5B,UAAU,GAAG,IAAI,wBAAa,CAAC;QAC7B,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,GAAG;QACX,cAAc,EAAE;YACd,eAAe,EAAE,KAAK;YACtB,gBAAgB,EAAE,IAAI;YACtB,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;SAC5C;QACD,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,uBAAuB,CAAC;QACnD,KAAK,EAAE,sBAAsB;QAC7B,IAAI,EAAE,KAAK;QACX,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;QACjB,WAAW,EAAE,IAAI;QACjB,QAAQ,EAAE,IAAI;QACd,WAAW,EAAE,KAAK,EAAE,+BAA+B;QACnD,WAAW,EAAE,KAAK;KACnB,CAAC,CAAC;IAEH,eAAe;IACf,IAAI,KAAK,EAAE,CAAC;QACV,UAAU,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QAC5C,+BAA+B;QAC/B,UAAU,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;IACxC,CAAC;SAAM,CAAC;QACN,qCAAqC;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;QAEtD,IAAI,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7B,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,oDAAoD;YACpD,MAAM,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDAyC2B,SAAS;iDACX,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;;;;;;;;;;;;;gFAaR,SAAS;;;;OAIlF,CAAC;YACF,UAAU,CAAC,OAAO,CAAC,gCAAgC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;IAED,yBAAyB;IACzB,UAAU,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE;QACpC,IAAI,UAAU,EAAE,CAAC;YACf,UAAU,CAAC,IAAI,EAAE,CAAC;YAClB,UAAU,CAAC,KAAK,EAAE,CAAC;YACnB,UAAU,CAAC,MAAM,EAAE,CAAC;YAEpB,sCAAsC;YACtC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAChC,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,UAAU,EAAE,CAAC;oBACf,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC,EAAE,IAAI,CAAC,CAAC;QACX,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,0DAA0D;IAC1D,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;QAC/B,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,CAAC,IAAI,EAAE,CAAC;YACpB,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,qBAAqB;IACrB,UAAU,EAAE,CAAC;AACf,CAAC;AAED,SAAS,UAAU;IACjB,wDAAwD;IACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAAC;IAC/D,MAAM,QAAQ,GAAG,sBAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IAEtD,IAAI,GAAG,IAAI,eAAI,CAAC,QAAQ,CAAC,CAAC;IAE1B,MAAM,WAAW,GAAG,eAAI,CAAC,iBAAiB,CAAC;QACzC;YACE,KAAK,EAAE,2BAA2B;YAClC,KAAK,EAAE,GAAG,EAAE;gBACV,IAAI,UAAU,EAAE,CAAC;oBACf,UAAU,CAAC,IAAI,EAAE,CAAC;oBAClB,UAAU,CAAC,KAAK,EAAE,CAAC;gBACrB,CAAC;YACH,CAAC;SACF;QACD;YACE,KAAK,EAAE,cAAc;YACrB,KAAK,EAAE,GAAG,EAAE;gBACV,IAAI,UAAU,EAAE,CAAC;oBACf,UAAU,CAAC,IAAI,EAAE,CAAC;gBACpB,CAAC;YACH,CAAC;SACF;QACD,EAAE,IAAI,EAAE,WAAW,EAAE;QACrB;YACE,KAAK,EAAE,qBAAqB;YAC5B,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,mBAAmB;oBAC1B,KAAK,EAAE,KAAK,IAAI,EAAE;wBAChB,IAAI,CAAC;4BACH,MAAM,YAAY,CAAC,MAAM,EAAE,CAAC;4BAC5B,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;wBACpC,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;wBACrD,CAAC;oBACH,CAAC;iBACF;gBACD;oBACE,KAAK,EAAE,oBAAoB;oBAC3B,KAAK,EAAE,KAAK,IAAI,EAAE;wBAChB,IAAI,CAAC;4BACH,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;4BAC7B,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;wBACrC,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;wBACtD,CAAC;oBACH,CAAC;iBACF;aACF;SACF;QACD,EAAE,IAAI,EAAE,WAAW,EAAE;QACrB;YACE,KAAK,EAAE,MAAM;YACb,KAAK,EAAE,GAAG,EAAE;gBACV,SAAS,GAAG,IAAI,CAAC;gBACjB,cAAG,CAAC,IAAI,EAAE,CAAC;YACb,CAAC;SACF;KACF,CAAC,CAAC;IAEH,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC;IACxC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IAEjC,8BAA8B;IAC9B,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,GAAG,EAAE;QAC3B,IAAI,UAAU,EAAE,CAAC;YACf,UAAU,CAAC,IAAI,EAAE,CAAC;YAClB,UAAU,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,8BAA8B;AAC9B,SAAS,cAAc,CAAC,cAAsB,EAAE,cAAwB,EAAE;IACxE,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;IAEzF,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE,CAAC;QAC7B,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,OAAO,QAAQ,CAAC;QAClB,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,eAAe;AACf,kBAAO,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;IACxE,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,KAAK,UAAU,GAAG,CAAC,CAAC;QAE7D,sBAAsB;QACtB,MAAM,cAAc,GAAG,cAAc,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAEzD,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,cAAc,UAAU,8BAA8B;aAC9D,CAAC;QACJ,CAAC;QAED,yBAAyB;QACzB,MAAM,KAAK,GAAG,IAAA,qBAAK,EAAC,cAAc,EAAE,EAAE,EAAE;YACtC,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,QAAQ;SAChB,CAAC,CAAC;QAEH,KAAK,CAAC,KAAK,EAAE,CAAC;QAEd,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,SAAS,cAAc,EAAE,CAAC,CAAC;QAErE,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,yBAAyB,IAAI,EAAE;YACxC,IAAI,EAAE,cAAc;SACrB,CAAC;IACJ,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CAAC,mBAAmB,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;QACjD,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC;IACJ,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kBAAO,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE;IAChE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC7B,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,8BAA8B,CAAC,CAAC;QAExE,kCAAkC;QAClC,IAAA,oBAAI,EAAC,aAAa,UAAU,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;YACzD,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,KAAK,CAAC,kBAAkB,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gBAChD,OAAO,CAAC;oBACN,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;iBACrB,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,sBAAsB,CAAC,CAAC;YAEjE,OAAO,CAAC;gBACN,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,wBAAwB,IAAI,8BAA8B;aACpE,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,kBAAO,CAAC,MAAM,CAAC,eAAe,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;IACnD,gBAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC;AAEH,uBAAuB;AACvB,kBAAO,CAAC,MAAM,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;IAClD,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,MAAM,YAAY,CAAC,SAAS,EAAE,CAAC;QACjD,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IAChC,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC3D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;IAClD,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kBAAO,CAAC,MAAM,CAAC,oBAAoB,EAAE,KAAK,IAAI,EAAE;IAC9C,IAAI,CAAC;QACH,MAAM,YAAY,CAAC,MAAM,EAAE,CAAC;QAC5B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;IACxE,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;IAClD,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kBAAO,CAAC,MAAM,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;IAC/C,IAAI,CAAC;QACH,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;QAC7B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;IACzE,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACrD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;IAClD,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,6BAA6B;AAC7B,kBAAO,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,IAAI,EAAE;IACvC,IAAI,UAAU,EAAE,CAAC;QACf,UAAU,CAAC,IAAI,EAAE,CAAC;QAClB,UAAU,CAAC,KAAK,EAAE,CAAC;QACnB,UAAU,CAAC,MAAM,EAAE,CAAC;QACpB,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChC,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACnC,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,CAAC;QACT,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IACD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC;AAC3D,CAAC,CAAC,CAAC;AAEH,kBAAO,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,IAAI,EAAE;IACvC,IAAI,UAAU,EAAE,CAAC;QACf,UAAU,CAAC,IAAI,EAAE,CAAC;QAClB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IACD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC;AAC3D,CAAC,CAAC,CAAC;AAEH,kBAAO,CAAC,MAAM,CAAC,qBAAqB,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE;IAC3E,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,8BAA8B,UAAU,EAAE,CAAC,CAAC;QAExD,kBAAkB;QAClB,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE,CAAC;YAC7B,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,OAAO,CAAC,GAAG,CAAC,SAAS,UAAU,QAAQ,QAAQ,EAAE,CAAC,CAAC;gBAEnD,qCAAqC;gBACrC,IAAI,OAAO,GAAG,YAAY,CAAC;gBAC3B,IAAI,CAAC;oBACH,qDAAqD;oBACrD,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBACpC,OAAO,GAAG,eAAe,KAAK,CAAC,KAAK,CAAC,kBAAkB,EAAE,GAAG,CAAC;gBAC/D,CAAC;gBAAC,OAAO,YAAY,EAAE,CAAC;oBACtB,wCAAwC;gBAC1C,CAAC;gBAED,OAAO;oBACL,KAAK,EAAE,IAAI;oBACX,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,OAAO;iBACjB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,GAAG,UAAU,kCAAkC,CAAC,CAAC;QAC7D,OAAO;YACL,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI;SACd,CAAC;IACJ,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CAAC,kBAAkB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;QACtD,OAAO;YACL,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI;YACb,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC;IACJ,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,qBAAqB;AACrB,cAAG,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;IACxB,YAAY,EAAE,CAAC;IAEf,8CAA8C;IAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,YAAY,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;YAC1C,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;gBACtD,YAAY,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBACpC,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;gBACxD,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,cAAG,CAAC,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;IAC/B,8DAA8D;IAC9D,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAClC,OAAO;IACT,CAAC;IAED,yCAAyC;IACzC,IAAI,IAAI,EAAE,CAAC;QACT,OAAO;IACT,CAAC;IAED,cAAG,CAAC,IAAI,EAAE,CAAC;AACb,CAAC,CAAC,CAAC;AAEH,cAAG,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE;IACtB,IAAI,wBAAa,CAAC,aAAa,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC/C,YAAY,EAAE,CAAC;IACjB,CAAC;SAAM,IAAI,UAAU,EAAE,CAAC;QACtB,UAAU,CAAC,IAAI,EAAE,CAAC;QAClB,UAAU,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,6BAA6B;AAC7B,MAAM,UAAU,GAAG,cAAG,CAAC,yBAAyB,EAAE,CAAC;AAEnD,IAAI,CAAC,UAAU,EAAE,CAAC;IAChB,cAAG,CAAC,IAAI,EAAE,CAAC;AACb,CAAC;KAAM,CAAC;IACN,cAAG,CAAC,EAAE,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC7B,mEAAmE;QACnE,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,UAAU,CAAC,WAAW,EAAE;gBAAE,UAAU,CAAC,OAAO,EAAE,CAAC;YACnD,UAAU,CAAC,KAAK,EAAE,CAAC;YACnB,UAAU,CAAC,IAAI,EAAE,CAAC;QACpB,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,uCAAuC;AACvC,cAAG,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;IACnB,uCAAuC;IACvC,MAAM,gBAAgB,GAAG,cAAG,CAAC,oBAAoB,EAAE,CAAC,gBAAgB,CAAC;IAErE,IAAI,gBAAgB,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,wCAAwC;QACxC,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,CAAC,IAAI,EAAE,CAAC;gBAClB,UAAU,CAAC,KAAK,EAAE,CAAC;gBACnB,UAAU,CAAC,MAAM,EAAE,CAAC;YACtB,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,iDAAiD;IAC7D,CAAC;AACH,CAAC,CAAC,CAAC"}