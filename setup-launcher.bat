@echo off
echo ========================================
echo PAS Auto Control Hub - Launcher Setup
echo ========================================
echo.

set APP_NAME=PAS Auto Control Hub
set APP_PATH=%~dp0startup-pas-hub.bat
set STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup
set REGISTRY_KEY=HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Run

echo Current directory: %~dp0
echo Launcher script: %APP_PATH%
echo Startup folder: %STARTUP_FOLDER%
echo.

:menu
echo ========================================
echo LAUNCHER SETUP OPTIONS
echo ========================================
echo.
echo 1. Enable Auto-Start (Registry Method) - RECOMMENDED
echo 2. Enable Auto-Start (Startup Folder Method)
echo 3. Disable Auto-Start (All Methods)
echo 4. Check Auto-Start Status
echo 5. Test Launcher Script
echo 6. Build Application for Production
echo 7. Exit
echo.
set /p choice="Select option (1-7): "

if "%choice%"=="1" goto enable_registry
if "%choice%"=="2" goto enable_folder
if "%choice%"=="3" goto disable_all
if "%choice%"=="4" goto check_status
if "%choice%"=="5" goto test_launcher
if "%choice%"=="6" goto build_app
if "%choice%"=="7" goto exit
echo [ERROR] Invalid choice!
goto menu

:enable_registry
echo.
echo [INFO] Enabling auto-start via Windows Registry...
reg add "%REGISTRY_KEY%" /v "%APP_NAME%" /t REG_SZ /d "%APP_PATH%" /f >nul 2>&1
if %errorlevel%==0 (
    echo [SUCCESS] Auto-start enabled via Registry!
    echo [INFO] PAS Auto Control Hub will start automatically with Windows.
) else (
    echo [ERROR] Failed to enable auto-start via Registry!
    echo [INFO] You may need to run as Administrator.
)
echo.
pause
goto menu

:enable_folder
echo.
echo [INFO] Enabling auto-start via Startup Folder...
copy "%APP_PATH%" "%STARTUP_FOLDER%\%APP_NAME%.bat" >nul 2>&1
if %errorlevel%==0 (
    echo [SUCCESS] Auto-start enabled via Startup Folder!
    echo [INFO] File copied to: %STARTUP_FOLDER%\%APP_NAME%.bat
) else (
    echo [ERROR] Failed to copy to Startup Folder!
)
echo.
pause
goto menu

:disable_all
echo.
echo [INFO] Disabling auto-start (all methods)...

:: Remove from Registry
reg delete "%REGISTRY_KEY%" /v "%APP_NAME%" /f >nul 2>&1
if %errorlevel%==0 (
    echo [SUCCESS] Removed from Registry
) else (
    echo [INFO] Not found in Registry (already disabled)
)

:: Remove from Startup Folder
del "%STARTUP_FOLDER%\%APP_NAME%.bat" >nul 2>&1
if %errorlevel%==0 (
    echo [SUCCESS] Removed from Startup Folder
) else (
    echo [INFO] Not found in Startup Folder (already disabled)
)

echo [SUCCESS] Auto-start disabled successfully!
echo.
pause
goto menu

:check_status
echo.
echo [INFO] Checking auto-start status...
echo.

:: Check Registry
reg query "%REGISTRY_KEY%" /v "%APP_NAME%" >nul 2>&1
if %errorlevel%==0 (
    echo [✓] Registry Auto-start: ENABLED
    for /f "tokens=3*" %%a in ('reg query "%REGISTRY_KEY%" /v "%APP_NAME%" 2^>nul ^| find "%APP_NAME%"') do echo     Path: %%a %%b
) else (
    echo [✗] Registry Auto-start: DISABLED
)

:: Check Startup Folder
if exist "%STARTUP_FOLDER%\%APP_NAME%.bat" (
    echo [✓] Startup Folder Auto-start: ENABLED
    echo     File: %STARTUP_FOLDER%\%APP_NAME%.bat
) else (
    echo [✗] Startup Folder Auto-start: DISABLED
)

echo.
pause
goto menu

:test_launcher
echo.
echo [INFO] Testing launcher script...
echo [INFO] This will start PAS Auto Control Hub...
echo.
call "%APP_PATH%"
echo.
echo [INFO] Launcher test completed.
pause
goto menu

:build_app
echo.
echo [INFO] Building application for production...
echo [INFO] This may take a few minutes...
echo.
call npm run build
if %errorlevel%==0 (
    echo [SUCCESS] Application built successfully!
    echo [INFO] Production files are ready in 'dist' folder.
) else (
    echo [ERROR] Build failed! Please check for errors above.
)
echo.
pause
goto menu

:exit
echo.
echo ========================================
echo Setup completed!
echo ========================================
echo.
echo To manually enable auto-start later:
echo 1. Run this script again, or
echo 2. Use the tray menu in PAS Auto Control Hub
echo.
echo Thank you for using PAS Auto Control Hub!
pause
exit

:error
echo [ERROR] An error occurred during setup.
pause
exit /b 1
