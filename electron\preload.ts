import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON><PERSON>er without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  launchApp: (executable: string, name: string) => 
    ipc<PERSON>enderer.invoke('launch-app', { executable, name }),
  
  startApp: (executable: string, name: string) => 
    ipcRenderer.invoke('start-app', { executable, name }),
  
  openExternal: (url: string) => 
    ipcRenderer.invoke('open-external', url),
  
  // Platform info
  platform: process.platform,
  isElectron: true
});

// Type definitions for the exposed API
declare global {
  interface Window {
    electronAPI: {
      launchApp: (executable: string, name: string) => Promise<{
        success: boolean;
        message?: string;
        error?: string;
        path?: string;
      }>;
      startApp: (executable: string, name: string) => Promise<{
        success: boolean;
        message?: string;
        error?: string;
      }>;
      openExternal: (url: string) => Promise<void>;
      platform: string;
      isElectron: boolean;
    };
  }
}
