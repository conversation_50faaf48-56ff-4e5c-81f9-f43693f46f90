import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON><PERSON><PERSON> without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  launchApp: (executable: string, name: string, paths: string[]) =>
    ipc<PERSON><PERSON>er.invoke('launch-app', { executable, name, paths }),

  startApp: (executable: string, name: string) =>
    ipcRenderer.invoke('start-app', { executable, name }),

  openExternal: (url: string) =>
    ipc<PERSON>enderer.invoke('open-external', url),

  checkAppInstalled: (executable: string, paths: string[]) =>
    ipcRenderer.invoke('check-app-installed', { executable, paths }),

  // Platform info
  platform: process.platform,
  isElectron: true
});

// Type definitions for the exposed API
declare global {
  interface Window {
    electronAPI: {
      launchApp: (executable: string, name: string, paths: string[]) => Promise<{
        success: boolean;
        message?: string;
        error?: string;
        path?: string;
      }>;
      startApp: (executable: string, name: string) => Promise<{
        success: boolean;
        message?: string;
        error?: string;
      }>;
      openExternal: (url: string) => Promise<void>;
      checkAppInstalled: (executable: string, paths: string[]) => Promise<{
        found: boolean;
        path?: string;
        version?: string;
        error?: string;
      }>;
      platform: string;
      isElectron: boolean;
    };
  }
}
